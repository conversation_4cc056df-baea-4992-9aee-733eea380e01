import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import '../utils/app_logger.dart';

/// خدمة معلومات التطبيق والجهاز
class AppInfoService {
  static final AppInfoService _instance = AppInfoService._internal();
  factory AppInfoService() => _instance;
  AppInfoService._internal();

  PackageInfo? _packageInfo;
  Map<String, dynamic>? _deviceInfo;
  bool _isInitialized = false;

  // معلومات التطبيق
  String get appName => _packageInfo?.appName ?? 'Legal 2025';
  String get packageName => _packageInfo?.packageName ?? 'com.legal2025.app';
  String get version => _packageInfo?.version ?? '1.0.0';
  String get buildNumber => _packageInfo?.buildNumber ?? '1';
  String get fullVersion => '$version+$buildNumber';

  // معلومات الجهاز
  Map<String, dynamic> get deviceInfo => _deviceInfo ?? {};
  bool get isInitialized => _isInitialized;

  /// تهيئة خدمة معلومات التطبيق
  Future<void> initialize() async {
    try {
      await _loadPackageInfo();
      await _loadDeviceInfo();
      _isInitialized = true;

      AppLogger.success(
        'تم تهيئة معلومات التطبيق: $appName v$fullVersion',
        'AppInfoService',
      );
    } catch (e) {
      AppLogger.error('خطأ في تهيئة معلومات التطبيق', 'AppInfoService', e);
    }
  }

  /// تحميل معلومات الحزمة
  Future<void> _loadPackageInfo() async {
    try {
      _packageInfo = await PackageInfo.fromPlatform();
    } catch (e) {
      AppLogger.error('خطأ في تحميل معلومات الحزمة', 'AppInfoService', e);
    }
  }

  /// تحميل معلومات الجهاز
  Future<void> _loadDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        _deviceInfo = {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'product': androidInfo.product,
          'androidId': androidInfo.id,
          'androidVersion': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
          'supportedAbis': androidInfo.supportedAbis,
          'systemFeatures': androidInfo.systemFeatures,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        _deviceInfo = {
          'platform': 'iOS',
          'name': iosInfo.name,
          'model': iosInfo.model,
          'localizedModel': iosInfo.localizedModel,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'identifierForVendor': iosInfo.identifierForVendor,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
          'utsname': {
            'machine': iosInfo.utsname.machine,
            'nodename': iosInfo.utsname.nodename,
            'release': iosInfo.utsname.release,
            'sysname': iosInfo.utsname.sysname,
            'version': iosInfo.utsname.version,
          },
        };
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfoPlugin.windowsInfo;
        _deviceInfo = {
          'platform': 'Windows',
          'computerName': windowsInfo.computerName,
          'numberOfCores': windowsInfo.numberOfCores,
          'systemMemoryInMegabytes': windowsInfo.systemMemoryInMegabytes,
          'userName': windowsInfo.userName,
          'majorVersion': windowsInfo.majorVersion,
          'minorVersion': windowsInfo.minorVersion,
          'buildNumber': windowsInfo.buildNumber,
          'platformId': windowsInfo.platformId,
          'csdVersion': windowsInfo.csdVersion,
          'servicePackMajor': windowsInfo.servicePackMajor,
          'servicePackMinor': windowsInfo.servicePackMinor,
          'suitMask': windowsInfo.suitMask,
          'productType': windowsInfo.productType,
          'reserved': windowsInfo.reserved,
          'buildLab': windowsInfo.buildLab,
          'buildLabEx': windowsInfo.buildLabEx,
          'digitalProductId': windowsInfo.digitalProductId,
          'displayVersion': windowsInfo.displayVersion,
          'editionId': windowsInfo.editionId,
          'installDate': windowsInfo.installDate,
          'productId': windowsInfo.productId,
          'productName': windowsInfo.productName,
          'registeredOwner': windowsInfo.registeredOwner,
          'releaseId': windowsInfo.releaseId,
          'deviceId': windowsInfo.deviceId,
        };
      } else {
        _deviceInfo = {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        };
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل معلومات الجهاز', 'AppInfoService', e);
      _deviceInfo = {
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
        'error': e.toString(),
      };
    }
  }

  /// الحصول على معلومات التطبيق كاملة
  Map<String, dynamic> getAppInfo() {
    return {
      'app': {
        'name': appName,
        'packageName': packageName,
        'version': version,
        'buildNumber': buildNumber,
        'fullVersion': fullVersion,
      },
      'device': deviceInfo,
      'platform': {
        'operatingSystem': Platform.operatingSystem,
        'operatingSystemVersion': Platform.operatingSystemVersion,
        'localHostname': Platform.localHostname,
        'numberOfProcessors': Platform.numberOfProcessors,
        'pathSeparator': Platform.pathSeparator,
        'localeName': Platform.localeName,
      },
      'runtime': {
        'dartVersion': Platform.version,
        'isDebugMode': _isDebugMode(),
        'isProfileMode': _isProfileMode(),
        'isReleaseMode': _isReleaseMode(),
      },
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// التحقق من وضع التطوير
  bool _isDebugMode() {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// التحقق من وضع الملف الشخصي
  bool _isProfileMode() {
    return !_isDebugMode() && !_isReleaseMode();
  }

  /// التحقق من وضع الإنتاج
  bool _isReleaseMode() {
    return const bool.fromEnvironment('dart.vm.product');
  }

  /// الحصول على معرف فريد للجهاز
  String? getDeviceId() {
    if (Platform.isAndroid) {
      return deviceInfo['androidId'] as String?;
    } else if (Platform.isIOS) {
      return deviceInfo['identifierForVendor'] as String?;
    } else if (Platform.isWindows) {
      return deviceInfo['deviceId'] as String?;
    }
    return null;
  }

  /// الحصول على اسم الجهاز
  String getDeviceName() {
    if (Platform.isAndroid) {
      final manufacturer = deviceInfo['manufacturer'] ?? '';
      final model = deviceInfo['model'] ?? '';
      return '$manufacturer $model'.trim();
    } else if (Platform.isIOS) {
      return deviceInfo['name'] ?? 'iPhone';
    } else if (Platform.isWindows) {
      return deviceInfo['computerName'] ?? 'Windows PC';
    }
    return 'Unknown Device';
  }

  /// الحصول على إصدار النظام
  String getSystemVersion() {
    if (Platform.isAndroid) {
      return 'Android ${deviceInfo['androidVersion'] ?? 'Unknown'}';
    } else if (Platform.isIOS) {
      return '${deviceInfo['systemName'] ?? 'iOS'} ${deviceInfo['systemVersion'] ?? 'Unknown'}';
    } else if (Platform.isWindows) {
      return 'Windows ${deviceInfo['displayVersion'] ?? deviceInfo['productName'] ?? 'Unknown'}';
    }
    return Platform.operatingSystemVersion;
  }

  /// التحقق من أن الجهاز حقيقي (ليس محاكي)
  bool isPhysicalDevice() {
    return deviceInfo['isPhysicalDevice'] as bool? ?? true;
  }

  /// الحصول على معلومات مختصرة للتطبيق
  String getAppSummary() {
    return '$appName v$fullVersion على ${getDeviceName()} (${getSystemVersion()})';
  }

  /// تحديث معلومات التطبيق
  Future<void> refresh() async {
    _isInitialized = false;
    await initialize();
  }

  /// تصدير معلومات التطبيق كـ JSON
  String exportAsJson() {
    try {
      return '''
{
  "appInfo": ${_mapToJsonString(getAppInfo())},
  "exportedAt": "${DateTime.now().toIso8601String()}"
}''';
    } catch (e) {
      AppLogger.error('خطأ في تصدير معلومات التطبيق', 'AppInfoService', e);
      return '{"error": "${e.toString()}"}';
    }
  }

  /// تحويل Map إلى JSON string بسيط
  String _mapToJsonString(Map<String, dynamic> map) {
    final buffer = StringBuffer('{');
    final entries = map.entries.toList();

    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      buffer.write('"${entry.key}": ');

      if (entry.value is String) {
        buffer.write('"${entry.value}"');
      } else if (entry.value is Map) {
        buffer.write(_mapToJsonString(entry.value as Map<String, dynamic>));
      } else if (entry.value is List) {
        buffer.write(
          '[${entry.value.map((e) => e is String ? '"$e"' : e.toString()).join(', ')}]',
        );
      } else {
        buffer.write(entry.value.toString());
      }

      if (i < entries.length - 1) {
        buffer.write(', ');
      }
    }

    buffer.write('}');
    return buffer.toString();
  }
}
