import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/notification_model.dart';
import '../utils/app_logger.dart';

/// خدمة الإشعارات المخصصة لـ PDF
class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Collections
  static const String _notificationsCollection = 'pdf_notifications';
  static const String _topicNotificationsCollection = 'topic_notifications';

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    try {
      // طلب أذونات الإشعارات
      await _requestPermissions();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // معالجة الإشعارات في الخلفية
      FirebaseMessaging.onBackgroundMessage(
        _firebaseMessagingBackgroundHandler,
      );

      if (kDebugMode) {
        print('✅ تم تهيئة خدمة إشعارات PDF بنجاح');
      }
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة الإشعارات', 'NotificationService', e);
    }
  }

  /// طلب أذونات الإشعارات
  static Future<void> _requestPermissions() async {
    try {
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (kDebugMode) {
        print('🔔 حالة أذونات الإشعارات: ${settings.authorizationStatus}');
      }
    } catch (e) {
      AppLogger.error('خطأ في طلب أذونات الإشعارات', 'NotificationService', e);
    }
  }

  /// تهيئة الإشعارات المحلية
  static Future<void> _initializeLocalNotifications() async {
    try {
      const androidSettings = AndroidInitializationSettings(
        '@mipmap/ic_launcher',
      );
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (kDebugMode) {
        print('✅ تم تهيئة الإشعارات المحلية');
      }
    } catch (e) {
      AppLogger.error(
        'خطأ في تهيئة الإشعارات المحلية',
        'NotificationService',
        e,
      );
    }
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('🔔 تم النقر على الإشعار: ${response.payload}');
    }
    // يمكن إضافة منطق التنقل هنا
  }

  /// الاشتراك في إشعارات السنة الدراسية
  static Future<void> subscribeToYearNotifications(String academicYear) async {
    try {
      if (academicYear.isEmpty) return;

      final topic = _getTopicName(academicYear);
      await _messaging.subscribeToTopic(topic);

      if (kDebugMode) {
        print('✅ تم الاشتراك في إشعارات السنة: $academicYear (Topic: $topic)');
      }
    } catch (e) {
      AppLogger.error(
        'خطأ في الاشتراك في إشعارات السنة',
        'NotificationService',
        e,
      );
    }
  }

  /// إلغاء الاشتراك من إشعارات السنة الدراسية
  static Future<void> unsubscribeFromYearNotifications(
    String academicYear,
  ) async {
    try {
      if (academicYear.isEmpty) return;

      final topic = _getTopicName(academicYear);
      await _messaging.unsubscribeFromTopic(topic);

      if (kDebugMode) {
        print('✅ تم إلغاء الاشتراك من إشعارات السنة: $academicYear');
      }
    } catch (e) {
      AppLogger.error(
        'خطأ في إلغاء الاشتراك من إشعارات السنة',
        'NotificationService',
        e,
      );
    }
  }

  /// تحديث اشتراك السنة الدراسية
  static Future<void> updateYearSubscription(
    String? oldYear,
    String newYear,
  ) async {
    try {
      // إلغاء الاشتراك من السنة القديمة
      if (oldYear != null && oldYear.isNotEmpty) {
        await unsubscribeFromYearNotifications(oldYear);
      }

      // الاشتراك في السنة الجديدة
      if (newYear.isNotEmpty) {
        await subscribeToYearNotifications(newYear);
      }

      if (kDebugMode) {
        print('✅ تم تحديث اشتراك السنة من "$oldYear" إلى "$newYear"');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث اشتراك السنة', 'NotificationService', e);
    }
  }

  /// إرسال إشعار ملف PDF جديد
  static Future<void> sendNewFileNotification({
    required String fileName,
    required String subjectName,
    required String academicYear,
    required String category,
    required String fileUrl,
  }) async {
    try {
      final title = 'ملف جديد في $subjectName 📄';
      final body = 'تم إضافة "$fileName" في مادة $subjectName - $category';

      final data = {
        'type': 'pdf_update',
        'action': 'added',
        'yearName': academicYear,
        'subjectName': subjectName,
        'pdfName': fileName,
        'category': category,
        'fileUrl': fileUrl,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // إرسال للموضوع (Topic)
      final topic = _getTopicName(academicYear);
      await _sendToTopic(topic, title, body, data);

      // حفظ في قاعدة البيانات
      await _saveNotificationToDatabase(
        title: title,
        body: body,
        academicYear: academicYear,
        data: data,
      );

      // إرسال إشعار محلي إذا كان التطبيق مفتوح
      await _showLocalNotification(title, body, data);

      if (kDebugMode) {
        print('✅ تم إرسال إشعار PDF جديد: $fileName');
        print('📄 للفرقة: $academicYear');
        print('📚 المادة: $subjectName');
      }
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار PDF جديد', 'NotificationService', e);
    }
  }

  /// إرسال إشعار لموضوع معين
  static Future<void> _sendToTopic(
    String topic,
    String title,
    String body,
    Map<String, dynamic> data,
  ) async {
    try {
      // حفظ في collection خاص للـ Cloud Function
      await _firestore.collection(_topicNotificationsCollection).add({
        'topic': topic,
        'title': title,
        'body': body,
        'data': data,
        'timestamp': FieldValue.serverTimestamp(),
        'sent': false,
      });

      if (kDebugMode) {
        print('✅ تم حفظ إشعار الموضوع: $topic');
      }
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار الموضوع', 'NotificationService', e);
    }
  }

  /// حفظ الإشعار في قاعدة البيانات
  static Future<void> _saveNotificationToDatabase({
    required String title,
    required String body,
    required String academicYear,
    required Map<String, dynamic> data,
  }) async {
    try {
      final notification = NotificationModel(
        id: '',
        userId: 'all_$academicYear', // للجميع في هذه السنة
        title: title,
        body: body,
        type: NotificationType.newFile,
        data: data,
        createdAt: DateTime.now(),
        isRead: false,
      );

      await _firestore.collection(_notificationsCollection).add({
        'userId': notification.userId,
        'title': notification.title,
        'body': notification.body,
        'type': notification.type.toString().split('.').last,
        'data': notification.data,
        'createdAt': notification.createdAt.toIso8601String(),
        'isRead': notification.isRead,
      });

      if (kDebugMode) {
        print('✅ تم حفظ الإشعار في قاعدة البيانات');
      }
    } catch (e) {
      AppLogger.error('خطأ في حفظ الإشعار', 'NotificationService', e);
    }
  }

  /// عرض إشعار محلي
  static Future<void> _showLocalNotification(
    String title,
    String body,
    Map<String, dynamic> data,
  ) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'pdf_notifications',
        'إشعارات PDF',
        channelDescription: 'إشعارات الملفات الجديدة',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: data.toString(),
      );

      if (kDebugMode) {
        print('✅ تم عرض الإشعار المحلي');
      }
    } catch (e) {
      AppLogger.error('خطأ في عرض الإشعار المحلي', 'NotificationService', e);
    }
  }

  /// الحصول على اسم الموضوع للسنة الدراسية
  static String _getTopicName(String academicYear) {
    return 'year_grade_$academicYear';
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  static Stream<int> getUnreadNotificationsCount() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return Stream.value(0);

    return _firestore
        .collection(_notificationsCollection)
        .where(
          'userId',
          whereIn: [
            'all_الفرقة الأولى',
            'all_الفرقة الثانية',
            'all_الفرقة الثالثة',
            'all_الفرقة الرابعة',
          ],
        )
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  /// الحصول على الإشعارات للمستخدم
  static Stream<List<NotificationModel>> getNotificationsForUser(
    String academicYear,
  ) {
    return _firestore
        .collection(_notificationsCollection)
        .where('userId', isEqualTo: 'all_$academicYear')
        .orderBy('createdAt', descending: true)
        .limit(10)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) {
                final data = doc.data();
                return NotificationModel(
                  id: doc.id,
                  userId: data['userId'] ?? '',
                  title: data['title'] ?? '',
                  body: data['body'] ?? '',
                  type: _parseNotificationType(data['type']),
                  data: Map<String, dynamic>.from(data['data'] ?? {}),
                  createdAt: DateTime.parse(
                    data['createdAt'] ?? DateTime.now().toIso8601String(),
                  ),
                  isRead: data['isRead'] ?? false,
                );
              }).toList(),
        );
  }

  /// تحديد الإشعار كمقروء
  static Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(_notificationsCollection)
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      AppLogger.error('خطأ في تحديد الإشعار كمقروء', 'NotificationService', e);
    }
  }

  /// تحليل نوع الإشعار من النص
  static NotificationType _parseNotificationType(String? typeString) {
    switch (typeString) {
      case 'newFile':
        return NotificationType.newFile;
      case 'like':
        return NotificationType.like;
      case 'comment':
        return NotificationType.comment;
      case 'share':
        return NotificationType.share;
      case 'newPost':
        return NotificationType.newPost;
      case 'general':
        return NotificationType.general;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.newFile;
    }
  }
}

/// معالج الإشعارات في الخلفية
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('📱 تم استقبال إشعار في الخلفية: ${message.messageId}');
  }
}
