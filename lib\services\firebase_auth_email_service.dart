import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/app_logger.dart';

/// خدمة Firebase Auth الحقيقية لإرسال كود التحقق عبر البريد الإلكتروني
/// مثل Facebook وInstagram تماماً
class FirebaseAuthEmailService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إرسال كود التحقق الحقيقي عبر Firebase Auth
  static Future<String?> sendVerificationCode(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      AppLogger.info(
        '🔥 بدء إرسال كود التحقق الحقيقي عبر Firebase Auth...',
        'FirebaseAuthEmail',
      );

      // الخطوة 1: إنشاء المستخدم في Firebase Auth
      final UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(
            email: email.trim(),
            password: password,
          );

      final User? user = userCredential.user;
      if (user == null) {
        throw Exception('فشل في إنشاء المستخدم');
      }

      AppLogger.success(
        '✅ تم إنشاء المستخدم في Firebase Auth',
        'FirebaseAuthEmail',
      );

      // الخطوة 2: تحديث اسم المستخدم
      await user.updateDisplayName(displayName);

      // الخطوة 3: إرسال إيميل التحقق الحقيقي
      await user.sendEmailVerification();

      AppLogger.success(
        '📧 تم إرسال إيميل التحقق الحقيقي إلى: $email',
        'FirebaseAuthEmail',
      );

      // الخطوة 4: حفظ بيانات المستخدم في Firestore
      await _saveUserData(user, displayName);

      // الخطوة 5: تسجيل خروج مؤقت حتى يتم التحقق
      await _auth.signOut();

      AppLogger.success(
        '🎯 تم إرسال إيميل التحقق بنجاح! تحقق من بريدك الإلكتروني',
        'FirebaseAuthEmail',
      );

      // إرجاع معرف المستخدم للمتابعة
      return user.uid;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('❌ خطأ Firebase Auth: ${e.code}', 'FirebaseAuthEmail', e);

      // معالجة أخطاء Firebase المحددة
      switch (e.code) {
        case 'email-already-in-use':
          throw Exception('هذا البريد الإلكتروني مسجل بالفعل');
        case 'weak-password':
          throw Exception('كلمة المرور ضعيفة جداً');
        case 'invalid-email':
          throw Exception('البريد الإلكتروني غير صحيح');
        case 'operation-not-allowed':
          throw Exception('التسجيل بالبريد الإلكتروني غير مفعل');
        default:
          throw Exception('خطأ في التسجيل: ${e.message}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ عام في إرسال كود التحقق', 'FirebaseAuthEmail', e);
      throw Exception('فشل في إرسال كود التحقق: $e');
    }
  }

  /// التحقق من حالة التحقق من البريد الإلكتروني
  static Future<bool> checkEmailVerification(
    String email,
    String password,
  ) async {
    try {
      AppLogger.info(
        '🔍 التحقق من حالة التحقق من البريد الإلكتروني...',
        'FirebaseAuthEmail',
      );

      // تسجيل الدخول للتحقق من حالة التحقق
      final UserCredential userCredential = await _auth
          .signInWithEmailAndPassword(email: email.trim(), password: password);

      final User? user = userCredential.user;
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      // إعادة تحميل بيانات المستخدم للحصول على أحدث حالة
      await user.reload();
      final User? refreshedUser = _auth.currentUser;

      if (refreshedUser?.emailVerified == true) {
        AppLogger.success(
          '✅ تم التحقق من البريد الإلكتروني بنجاح!',
          'FirebaseAuthEmail',
        );

        // تحديث حالة التحقق في Firestore
        await _updateVerificationStatus(refreshedUser!.uid, true);

        return true;
      } else {
        AppLogger.warning(
          '⚠️ لم يتم التحقق من البريد الإلكتروني بعد',
          'FirebaseAuthEmail',
        );

        // تسجيل خروج مؤقت
        await _auth.signOut();
        return false;
      }
    } on FirebaseAuthException catch (e) {
      AppLogger.error('❌ خطأ في التحقق: ${e.code}', 'FirebaseAuthEmail', e);

      switch (e.code) {
        case 'user-not-found':
          throw Exception('المستخدم غير موجود');
        case 'wrong-password':
          throw Exception('كلمة المرور غير صحيحة');
        case 'invalid-email':
          throw Exception('البريد الإلكتروني غير صحيح');
        case 'user-disabled':
          throw Exception('تم تعطيل هذا الحساب');
        default:
          throw Exception('خطأ في التحقق: ${e.message}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ عام في التحقق', 'FirebaseAuthEmail', e);
      throw Exception('فشل في التحقق: $e');
    }
  }

  /// إعادة إرسال إيميل التحقق
  static Future<bool> resendVerificationEmail(
    String email,
    String password,
  ) async {
    try {
      AppLogger.info('🔄 إعادة إرسال إيميل التحقق...', 'FirebaseAuthEmail');

      // تسجيل الدخول
      final UserCredential userCredential = await _auth
          .signInWithEmailAndPassword(email: email.trim(), password: password);

      final User? user = userCredential.user;
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      // إرسال إيميل التحقق مرة أخرى
      await user.sendEmailVerification();

      // تسجيل خروج مؤقت
      await _auth.signOut();

      AppLogger.success(
        '📧 تم إعادة إرسال إيميل التحقق بنجاح!',
        'FirebaseAuthEmail',
      );

      return true;
    } catch (e) {
      AppLogger.error(
        '❌ فشل في إعادة إرسال إيميل التحقق',
        'FirebaseAuthEmail',
        e,
      );
      return false;
    }
  }

  /// حفظ بيانات المستخدم في Firestore
  static Future<void> _saveUserData(User user, String displayName) async {
    try {
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'email': user.email,
        'displayName': displayName,
        'emailVerified': false,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'academicYear': 'الفرقة الأولى', // افتراضي
      });

      AppLogger.success(
        '💾 تم حفظ بيانات المستخدم في Firestore',
        'FirebaseAuthEmail',
      );
    } catch (e) {
      AppLogger.error('❌ فشل في حفظ بيانات المستخدم', 'FirebaseAuthEmail', e);
    }
  }

  /// تحديث حالة التحقق في Firestore
  static Future<void> _updateVerificationStatus(
    String uid,
    bool verified,
  ) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'emailVerified': verified,
        'verifiedAt': verified ? FieldValue.serverTimestamp() : null,
      });

      AppLogger.success(
        '✅ تم تحديث حالة التحقق في Firestore',
        'FirebaseAuthEmail',
      );
    } catch (e) {
      AppLogger.error('❌ فشل في تحديث حالة التحقق', 'FirebaseAuthEmail', e);
    }
  }

  /// الحصول على المستخدم الحالي
  static User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// التحقق من حالة تسجيل الدخول
  static bool isUserLoggedIn() {
    return _auth.currentUser != null;
  }

  /// تسجيل الخروج
  static Future<void> signOut() async {
    await _auth.signOut();
    AppLogger.info('👋 تم تسجيل الخروج', 'FirebaseAuthEmail');
  }
}
