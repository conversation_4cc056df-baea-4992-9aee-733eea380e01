import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../providers/theme_provider.dart';
import '../utils/app_logger.dart';

class PollWidget extends StatefulWidget {
  final Map<String, dynamic> pollData;
  final String postId;
  final ThemeProvider themeProvider;

  const PollWidget({
    super.key,
    required this.pollData,
    required this.postId,
    required this.themeProvider,
  });

  @override
  State<PollWidget> createState() => _PollWidgetState();
}

class _PollWidgetState extends State<PollWidget> with TickerProviderStateMixin {
  String? _selectedOption;
  bool _hasVoted = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkIfVoted();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  void _checkIfVoted() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final voters = List<String>.from(widget.pollData['voters'] ?? []);
      _hasVoted = voters.contains(user.uid);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final options = List<String>.from(widget.pollData['options'] ?? []);
    final votes = Map<String, int>.from(widget.pollData['votes'] ?? {});
    final totalVotes = votes.values.fold(0, (sum, count) => sum + count);

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  widget.themeProvider.isDarkMode
                      ? const Color(0xFF0F172A)
                      : Colors.grey[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF10B981).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.poll, color: const Color(0xFF10B981), size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'استطلاع رأي',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF10B981),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '$totalVotes صوت',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            widget.themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (widget.pollData['question'] != null) ...[
                  Text(
                    widget.pollData['question'],
                    style: GoogleFonts.cairo(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color:
                          widget.themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black87,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  const SizedBox(height: 16),
                ],
                ...options.map(
                  (option) =>
                      _buildPollOption(option, votes[option] ?? 0, totalVotes),
                ),
                if (!_hasVoted && _selectedOption != null) ...[
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _submitVote,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF10B981),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        'تصويت',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPollOption(String option, int votes, int totalVotes) {
    final percentage = totalVotes > 0 ? (votes / totalVotes) : 0.0;
    final isSelected = _selectedOption == option;
    final showResults = _hasVoted || totalVotes > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: _hasVoted ? null : () => _selectOption(option),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color:
                widget.themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  isSelected
                      ? const Color(0xFF10B981)
                      : (widget.themeProvider.isDarkMode
                          ? Colors.grey[600]!
                          : Colors.grey[300]!),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Stack(
            children: [
              if (showResults)
                Positioned.fill(
                  child: FractionallySizedBox(
                    alignment: Alignment.centerRight,
                    widthFactor: percentage,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              Row(
                children: [
                  if (!_hasVoted)
                    Icon(
                      isSelected
                          ? Icons.radio_button_checked
                          : Icons.radio_button_unchecked,
                      color:
                          isSelected
                              ? const Color(0xFF10B981)
                              : (widget.themeProvider.isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[500]),
                      size: 20,
                    )
                  else
                    Icon(
                      Icons.check_circle,
                      color: const Color(0xFF10B981),
                      size: 20,
                    ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      option,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        color:
                            widget.themeProvider.isDarkMode
                                ? Colors.white
                                : Colors.black87,
                      ),
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                  if (showResults) ...[
                    const SizedBox(width: 8),
                    Text(
                      '${(percentage * 100).round()}%',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF10B981),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '$votes',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            widget.themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectOption(String option) {
    setState(() {
      _selectedOption = option;
    });
    HapticFeedback.lightImpact();
  }

  Future<void> _submitVote() async {
    if (_selectedOption == null) return;

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    setState(() {
      _hasVoted = true;
    });

    HapticFeedback.mediumImpact();

    // Implement vote submission to Firebase
    try {
      // Find the selected option index
      final options = List<String>.from(widget.pollData['options'] ?? []);
      final selectedIndex = options.indexOf(_selectedOption!);

      // In a real app, you would submit the vote to Firebase
      // Example implementation:
      // await FirebaseFirestore.instance
      //     .collection('community_poll_votes')
      //     .doc('${widget.postId}_${user.uid}')
      //     .set({
      //   'postId': widget.postId,
      //   'userId': user.uid,
      //   'optionIndex': selectedIndex,
      //   'selectedOption': _selectedOption,
      //   'timestamp': FieldValue.serverTimestamp(),
      // });

      // For now, we just update the local state
      AppLogger.info(
        'Vote submitted for option "$_selectedOption" (index $selectedIndex) in post ${widget.postId}',
        'PollWidget',
      );
    } catch (e) {
      AppLogger.error('Error submitting vote', 'PollWidget', e);
      // Revert the local state if Firebase submission fails
      setState(() {
        _selectedOption = null;
        _hasVoted = false;
      });
      _showSnackBar('حدث خطأ في تسجيل الصوت');
      return;
    }

    _showSnackBar('تم تسجيل صوتك بنجاح');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
