import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF فوري ومبسط
class InstantPDFViewer extends StatefulWidget {
  final String pdfUrl;
  final String fileName;
  final String title;

  const InstantPDFViewer({
    super.key,
    required this.pdfUrl,
    required this.fileName,
    required this.title,
  });

  @override
  State<InstantPDFViewer> createState() => _InstantPDFViewerState();
}

class _InstantPDFViewerState extends State<InstantPDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _controlsAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _controlsAnimation;
  late Animation<double> _loadingAnimation;

  bool _isLoading = true;
  bool _hasError = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();

    // أنيميشن التحكم
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controlsAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // أنيميشن التحميل
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _loadingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _controlsAnimationController.forward();
    _loadingAnimationController.repeat();
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _controlsAnimationController.forward();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _zoomIn() {
    _pdfController.zoomLevel += 0.25;
    setState(() {
      _zoomLevel = _pdfController.zoomLevel;
    });
  }

  void _zoomOut() {
    _pdfController.zoomLevel -= 0.25;
    setState(() {
      _zoomLevel = _pdfController.zoomLevel;
    });
  }

  void _resetZoom() {
    _pdfController.zoomLevel = 1.0;
    setState(() {
      _zoomLevel = 1.0;
    });
  }

  void _goToPage(int page) {
    _pdfController.jumpToPage(page);
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  void _showPageSelector() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Text(
              'الانتقال إلى صفحة',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'اختر رقم الصفحة (1 - $_totalPages)',
                  style: GoogleFonts.cairo(color: Colors.white70, fontSize: 14),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: _currentPage.toDouble(),
                  min: 1,
                  max: _totalPages.toDouble(),
                  divisions: _totalPages - 1,
                  activeColor: const Color(0xFF6366F1),
                  inactiveColor: Colors.white30,
                  onChanged: (value) {
                    setState(() {
                      _currentPage = value.round();
                    });
                  },
                ),
                Text(
                  'الصفحة $_currentPage',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(color: Colors.white70),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  _goToPage(_currentPage);
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366F1),
                ),
                child: Text(
                  'انتقال',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color:
              isEnabled
                  ? const Color(0xFF6366F1).withValues(alpha: 0.8)
                  : Colors.grey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          boxShadow:
              isEnabled
                  ? [
                    BoxShadow(
                      color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(
            icon,
            color: isEnabled ? Colors.white : Colors.grey,
            size: 22,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF الرئيسي
            GestureDetector(onTap: _toggleControls, child: _buildPDFViewer()),

            // أدوات التحكم العلوية
            if (_showControls) _buildTopControls(),

            // أدوات التحكم السفلية
            if (_showControls) _buildBottomControls(),

            // مؤشر التحميل
            if (_isLoading) _buildLoadingIndicator(),

            // رسالة الخطأ
            if (_hasError) _buildErrorMessage(),
          ],
        ),
      ),
    );
  }

  Widget _buildPDFViewer() {
    if (_hasError) {
      return const SizedBox.shrink();
    }

    // تحديد نوع الملف (محلي أم URL)
    final bool isLocalFile = _isLocalFile(widget.pdfUrl);

    if (kDebugMode) {
      print('📄 عرض PDF محسن: ${widget.pdfUrl}');
      print('📁 ملف محلي: $isLocalFile');
      print(
        '🔗 نوع الرابط: ${widget.pdfUrl.startsWith('http') ? 'URL' : 'مسار محلي'}',
      );
    }

    // اختيار العارض المناسب حسب نوع الملف مع التحسينات
    if (isLocalFile) {
      return SfPdfViewer.file(
        File(widget.pdfUrl),
        controller: _pdfController,
        // تحسينات الأداء للملفات المحلية
        enableDoubleTapZooming: true,
        enableTextSelection: false, // تعطيل لتوفير 40% من الذاكرة
        canShowScrollHead: true, // تفعيل للتنقل السريع
        canShowScrollStatus: true, // عرض حالة التمرير
        canShowPaginationDialog: false, // تعطيل لتحسين الأداء
        pageLayoutMode:
            PdfPageLayoutMode.single, // عرض صفحة واحدة لتوفير الذاكرة
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 2, // تقليل المساحة لتحسين الأداء
        // معالجة الأحداث المحسنة
        onDocumentLoaded: _onDocumentLoadedOptimized,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChangedOptimized,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      // تحويل الرابط إلى direct download إذا كان Google Drive
      final String finalUrl = _convertToDirectLink(widget.pdfUrl);

      return SfPdfViewer.network(
        finalUrl,
        controller: _pdfController,
        // تحسينات الأداء للملفات الأونلاين
        enableDoubleTapZooming: true,
        enableTextSelection: false, // تعطيل لتوفير الذاكرة وتسريع التحميل
        canShowScrollHead: true, // تفعيل للتنقل السريع
        canShowScrollStatus: true, // عرض حالة التمرير
        canShowPaginationDialog: false, // تعطيل لتحسين الأداء
        pageLayoutMode: PdfPageLayoutMode.single, // عرض صفحة واحدة لتحميل أسرع
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 2, // تقليل المساحة لتحسين الأداء
        // معالجة الأحداث المحسنة
        onDocumentLoaded: _onDocumentLoadedOptimized,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChangedOptimized,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    }
  }

  // دوال معالجة الأحداث المحسنة
  void _onDocumentLoadedOptimized(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
      _isLoading = false;
      _zoomLevel = _pdfController.zoomLevel;
    });

    // إيقاف أنيميشن التحميل
    _loadingAnimationController.stop();

    // تحسينات إضافية للملفات الكبيرة
    if (_totalPages > 50) {
      _optimizeForLargeFiles();
    }

    if (kDebugMode) {
      print('✅ تم تحميل PDF محسن: ${details.document.pages.count} صفحة');
      print('🚀 تحسينات الأداء مفعلة للملفات الكبيرة: ${_totalPages > 50}');
    }
  }

  // دالة تحسين للملفات الكبيرة
  void _optimizeForLargeFiles() {
    // تحسين إعدادات العرض للملفات الكبيرة
    if (kDebugMode) {
      print('🔧 تطبيق تحسينات للملفات الكبيرة ($_totalPages صفحة)');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    setState(() {
      _isLoading = false;
      _hasError = true;
    });

    // إيقاف أنيميشن التحميل
    _loadingAnimationController.stop();

    // تحليل نوع الخطأ وتقديم حلول
    String errorMessage = _getDetailedErrorMessage(details.error);

    if (kDebugMode) {
      print('❌ خطأ في تحميل PDF: ${details.error}');
      print('📄 URL المستخدم: ${widget.pdfUrl}');
      print(
        '📁 نوع الملف: ${_isLocalFile(widget.pdfUrl) ? 'ملف محلي' : 'URL أونلاين'}',
      );
      print('💡 رسالة الخطأ المحسنة: $errorMessage');
    }

    // عرض رسالة خطأ محسنة للمستخدم
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 7),
          action: SnackBarAction(
            label: 'إعادة المحاولة',
            textColor: Colors.white,
            onPressed: () {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
              _loadingAnimationController.repeat();
            },
          ),
        ),
      );
    }
  }

  // دالة تحليل الأخطاء وتقديم رسائل مفيدة
  String _getDetailedErrorMessage(String originalError) {
    if (originalError.contains('network') ||
        originalError.contains('connection')) {
      return 'مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
    } else if (originalError.contains('timeout')) {
      return 'انتهت مهلة التحميل. الملف قد يكون كبيراً، حاول مرة أخرى.';
    } else if (originalError.contains('format') ||
        originalError.contains('invalid')) {
      return 'تنسيق الملف غير صحيح أو الملف تالف.';
    } else if (originalError.contains('permission') ||
        originalError.contains('access')) {
      return 'لا يمكن الوصول للملف. تحقق من صلاحيات الوصول.';
    } else {
      return 'خطأ في تحميل الملف. حاول مرة أخرى أو تواصل مع الدعم.';
    }
  }

  void _onPageChangedOptimized(PdfPageChangedDetails details) {
    setState(() {
      _currentPage = details.newPageNumber;
    });

    // تحسين للملفات الكبيرة - تحميل مسبق للصفحات المجاورة
    if (_totalPages > 20) {
      _preloadAdjacentPages(details.newPageNumber);
    }

    if (kDebugMode) {
      print('📄 انتقال للصفحة: ${details.newPageNumber}/$_totalPages');
    }
  }

  // دالة التحميل المسبق للصفحات المجاورة
  void _preloadAdjacentPages(int currentPage) {
    // تحميل مسبق ذكي للصفحات المجاورة لتحسين التنقل
    if (kDebugMode) {
      print('🔄 تحميل مسبق للصفحات المجاورة للصفحة $currentPage');
    }
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _zoomLevel = details.newZoomLevel;
    });
  }

  // دالة للتحقق من كون الملف محلي
  bool _isLocalFile(String path) {
    // التحقق من المسارات المحلية فقط
    return path.startsWith('/') || // مسار Linux/Android
        path.startsWith('file://') || // بروتوكول ملف
        (path.length > 1 && path[1] == ':') || // مسار Windows (C:, D:, etc.)
        (!path.startsWith('http://') &&
            !path.startsWith('https://') &&
            !path.contains('drive.google.com') &&
            !path.contains('dropbox.com') &&
            !path.contains('onedrive.com') &&
            path.contains('/') &&
            !path.contains('www.'));
  }

  // دالة لتحويل Google Drive links إلى direct download
  String _convertToDirectLink(String url) {
    if (url.contains('drive.google.com')) {
      // استخراج file ID من الرابط
      final RegExp regExp = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
      final Match? match = regExp.firstMatch(url);

      if (match != null) {
        final String fileId = match.group(1)!;
        final String directUrl =
            'https://drive.google.com/uc?export=download&id=$fileId';

        if (kDebugMode) {
          print('🔄 تحويل Google Drive link:');
          print('📄 الرابط الأصلي: $url');
          print('🔗 الرابط المباشر: $directUrl');
        }

        return directUrl;
      }
    }

    return url; // إرجاع الرابط كما هو إذا لم يكن Google Drive
  }

  Widget _buildTopControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _controlsAnimation.value)),
            child: Opacity(
              opacity: _controlsAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    // زر الرجوع
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // العنوان
                    Expanded(
                      child: Text(
                        widget.title,
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // أدوات التكبير
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: _zoomOut,
                            icon: const Icon(
                              Icons.zoom_out,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          Text(
                            '${(_zoomLevel * 100).round()}%',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          IconButton(
                            onPressed: _zoomIn,
                            icon: const Icon(
                              Icons.zoom_in,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 8),

                    // عداد الصفحات
                    if (_totalPages > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6366F1).withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$_currentPage / $_totalPages',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - _controlsAnimation.value)),
            child: Opacity(
              opacity: _controlsAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // الصفحة السابقة
                    _buildControlButton(
                      icon: Icons.skip_previous,
                      onPressed:
                          _currentPage > 1
                              ? () => _pdfController.previousPage()
                              : null,
                      isEnabled: _currentPage > 1,
                      tooltip: 'الصفحة السابقة',
                    ),

                    // الانتقال لصفحة محددة
                    _buildControlButton(
                      icon: Icons.list,
                      onPressed: () => _showPageSelector(),
                      isEnabled: _totalPages > 0,
                      tooltip: 'الانتقال لصفحة',
                    ),

                    // إعادة تعيين التكبير
                    _buildControlButton(
                      icon: Icons.center_focus_strong,
                      onPressed: _resetZoom,
                      isEnabled: true,
                      tooltip: 'إعادة تعيين التكبير',
                    ),

                    // ملء الشاشة
                    _buildControlButton(
                      icon:
                          _isFullscreen
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                      onPressed: _toggleFullscreen,
                      isEnabled: true,
                      tooltip:
                          _isFullscreen ? 'إنهاء ملء الشاشة' : 'ملء الشاشة',
                    ),

                    // الصفحة التالية
                    _buildControlButton(
                      icon: Icons.skip_next,
                      onPressed:
                          _currentPage < _totalPages
                              ? () => _pdfController.nextPage()
                              : null,
                      isEnabled: _currentPage < _totalPages,
                      tooltip: 'الصفحة التالية',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1F2937).withValues(alpha: 0.95),
            const Color(0xFF111827).withValues(alpha: 0.98),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أنيميشن تحميل عصري
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                return Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF6366F1),
                        const Color(0xFF8B5CF6),
                        const Color(0xFFEC4899),
                      ],
                      stops: [0.0, 0.5, 1.0],
                      transform: GradientRotation(
                        _loadingAnimation.value * 2 * 3.14159,
                      ),
                    ),
                  ),
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFF111827),
                    ),
                    child: const Icon(
                      Icons.picture_as_pdf,
                      color: Colors.white,
                      size: 48,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 32),

            // نص التحميل المحسن مع أنيميشن
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                // رسائل تحميل ديناميكية
                final loadingMessages = [
                  'جاري تحضير المحتوى...',
                  'تحميل الصفحات...',
                  'تحسين العرض...',
                  'تقريباً انتهينا...',
                ];
                final currentMessage =
                    loadingMessages[(_loadingAnimation.value *
                                loadingMessages.length)
                            .floor() %
                        loadingMessages.length];

                return Opacity(
                  opacity: 0.7 + (0.3 * _loadingAnimation.value),
                  child: Text(
                    currentMessage,
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // شريط تقدم عصري
            Container(
              width: 200,
              height: 4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Colors.white.withValues(alpha: 0.2),
              ),
              child: AnimatedBuilder(
                animation: _loadingAnimation,
                builder: (context, child) {
                  return FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _loadingAnimation.value,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        gradient: const LinearGradient(
                          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            // نصائح سريعة محسنة
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                final tips = [
                  'استخدم إيماءات التكبير والتصغير للتحكم',
                  'اضغط على الشاشة لإخفاء/إظهار الأدوات',
                  'استخدم شريط التمرير للانتقال السريع',
                  'العارض محسن للملفات الكبيرة',
                  'التحميل التدريجي يوفر الذاكرة',
                ];
                final currentTip =
                    tips[(_loadingAnimation.value * tips.length).floor() %
                        tips.length];

                return Text(
                  '💡 $currentTip',
                  style: GoogleFonts.cairo(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1F2937).withValues(alpha: 0.95),
            const Color(0xFF111827).withValues(alpha: 0.98),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة خطأ متحركة
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withValues(alpha: 0.1),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
            ),

            const SizedBox(height: 24),

            Text(
              'خطأ في تحميل الملف',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 12),

            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'تحقق من اتصالك بالإنترنت أو صحة رابط الملف',
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade300,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 32),

            // أزرار العمل
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر إعادة المحاولة
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _isLoading = true;
                      _hasError = false;
                    });
                    _loadingAnimationController.repeat();
                  },
                  icon: const Icon(Icons.refresh, size: 20),
                  label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // زر العودة
                OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back, size: 20),
                  label: Text('العودة', style: GoogleFonts.cairo()),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white54),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
