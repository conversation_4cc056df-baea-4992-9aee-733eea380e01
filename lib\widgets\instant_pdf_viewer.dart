import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF فوري ومبسط
class InstantPDFViewer extends StatefulWidget {
  final String pdfUrl;
  final String fileName;
  final String title;

  const InstantPDFViewer({
    super.key,
    required this.pdfUrl,
    required this.fileName,
    required this.title,
  });

  @override
  State<InstantPDFViewer> createState() => _InstantPDFViewerState();
}

class _InstantPDFViewerState extends State<InstantPDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _controlsAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _controlsAnimation;
  late Animation<double> _loadingAnimation;

  bool _isLoading = true;
  bool _hasError = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();

    // أنيميشن التحكم
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controlsAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // أنيميشن التحميل
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _loadingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _controlsAnimationController.forward();
    _loadingAnimationController.repeat();
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _controlsAnimationController.forward();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _zoomIn() {
    _pdfController.zoomLevel += 0.25;
    setState(() {
      _zoomLevel = _pdfController.zoomLevel;
    });
  }

  void _zoomOut() {
    _pdfController.zoomLevel -= 0.25;
    setState(() {
      _zoomLevel = _pdfController.zoomLevel;
    });
  }

  void _resetZoom() {
    _pdfController.zoomLevel = 1.0;
    setState(() {
      _zoomLevel = 1.0;
    });
  }

  void _goToPage(int page) {
    _pdfController.jumpToPage(page);
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  void _showPageSelector() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2937),
            title: Text(
              'الانتقال إلى صفحة',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'اختر رقم الصفحة (1 - $_totalPages)',
                  style: GoogleFonts.cairo(color: Colors.white70, fontSize: 14),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: _currentPage.toDouble(),
                  min: 1,
                  max: _totalPages.toDouble(),
                  divisions: _totalPages - 1,
                  activeColor: const Color(0xFF6366F1),
                  inactiveColor: Colors.white30,
                  onChanged: (value) {
                    setState(() {
                      _currentPage = value.round();
                    });
                  },
                ),
                Text(
                  'الصفحة $_currentPage',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(color: Colors.white70),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  _goToPage(_currentPage);
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366F1),
                ),
                child: Text(
                  'انتقال',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color:
              isEnabled
                  ? const Color(0xFF6366F1).withValues(alpha: 0.8)
                  : Colors.grey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          boxShadow:
              isEnabled
                  ? [
                    BoxShadow(
                      color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(
            icon,
            color: isEnabled ? Colors.white : Colors.grey,
            size: 22,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF الرئيسي
            GestureDetector(onTap: _toggleControls, child: _buildPDFViewer()),

            // أدوات التحكم العلوية
            if (_showControls) _buildTopControls(),

            // أدوات التحكم السفلية
            if (_showControls) _buildBottomControls(),

            // مؤشر التحميل
            if (_isLoading) _buildLoadingIndicator(),

            // رسالة الخطأ
            if (_hasError) _buildErrorMessage(),
          ],
        ),
      ),
    );
  }

  Widget _buildPDFViewer() {
    if (_hasError) {
      return const SizedBox.shrink();
    }

    // تحديد نوع الملف (محلي أم URL)
    final bool isLocalFile = _isLocalFile(widget.pdfUrl);

    if (kDebugMode) {
      print('📄 عرض PDF: ${widget.pdfUrl}');
      print('📁 ملف محلي: $isLocalFile');
      print(
        '🔗 نوع الرابط: ${widget.pdfUrl.startsWith('http') ? 'URL' : 'مسار محلي'}',
      );
    }

    // اختيار العارض المناسب حسب نوع الملف
    if (isLocalFile) {
      return SfPdfViewer.file(
        File(widget.pdfUrl),
        controller: _pdfController,
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: false,
        canShowScrollStatus: false,
        canShowPaginationDialog: false,
        pageLayoutMode: PdfPageLayoutMode.single,
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 4,
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      // تحويل الرابط إلى direct download إذا كان Google Drive
      final String finalUrl = _convertToDirectLink(widget.pdfUrl);

      return SfPdfViewer.network(
        finalUrl,
        controller: _pdfController,
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: false,
        canShowScrollStatus: false,
        canShowPaginationDialog: false,
        pageLayoutMode: PdfPageLayoutMode.single,
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 4,
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    }
  }

  // دوال معالجة الأحداث المشتركة
  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
      _isLoading = false;
      _zoomLevel = _pdfController.zoomLevel;
    });
    // إيقاف أنيميشن التحميل
    _loadingAnimationController.stop();
    if (kDebugMode) {
      print('✅ تم تحميل PDF: ${details.document.pages.count} صفحة');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    setState(() {
      _isLoading = false;
      _hasError = true;
    });
    // إيقاف أنيميشن التحميل
    _loadingAnimationController.stop();
    if (kDebugMode) {
      print('❌ خطأ في تحميل PDF: ${details.error}');
      print('📄 URL المستخدم: ${widget.pdfUrl}');
      print(
        '📁 تم التعامل معه كـ: ${_isLocalFile(widget.pdfUrl) ? 'ملف محلي' : 'URL أونلاين'}',
      );
    }

    // عرض رسالة خطأ للمستخدم
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل الملف: ${details.error}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      _currentPage = details.newPageNumber;
    });
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _zoomLevel = details.newZoomLevel;
    });
  }

  // دالة للتحقق من كون الملف محلي
  bool _isLocalFile(String path) {
    // التحقق من المسارات المحلية فقط
    return path.startsWith('/') || // مسار Linux/Android
        path.startsWith('file://') || // بروتوكول ملف
        (path.length > 1 && path[1] == ':') || // مسار Windows (C:, D:, etc.)
        (!path.startsWith('http://') &&
            !path.startsWith('https://') &&
            !path.contains('drive.google.com') &&
            !path.contains('dropbox.com') &&
            !path.contains('onedrive.com') &&
            path.contains('/') &&
            !path.contains('www.'));
  }

  // دالة لتحويل Google Drive links إلى direct download
  String _convertToDirectLink(String url) {
    if (url.contains('drive.google.com')) {
      // استخراج file ID من الرابط
      final RegExp regExp = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
      final Match? match = regExp.firstMatch(url);

      if (match != null) {
        final String fileId = match.group(1)!;
        final String directUrl =
            'https://drive.google.com/uc?export=download&id=$fileId';

        if (kDebugMode) {
          print('🔄 تحويل Google Drive link:');
          print('📄 الرابط الأصلي: $url');
          print('🔗 الرابط المباشر: $directUrl');
        }

        return directUrl;
      }
    }

    return url; // إرجاع الرابط كما هو إذا لم يكن Google Drive
  }

  Widget _buildTopControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _controlsAnimation.value)),
            child: Opacity(
              opacity: _controlsAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    // زر الرجوع
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // العنوان
                    Expanded(
                      child: Text(
                        widget.title,
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // أدوات التكبير
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: _zoomOut,
                            icon: const Icon(
                              Icons.zoom_out,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          Text(
                            '${(_zoomLevel * 100).round()}%',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          IconButton(
                            onPressed: _zoomIn,
                            icon: const Icon(
                              Icons.zoom_in,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 8),

                    // عداد الصفحات
                    if (_totalPages > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6366F1).withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$_currentPage / $_totalPages',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - _controlsAnimation.value)),
            child: Opacity(
              opacity: _controlsAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // الصفحة السابقة
                    _buildControlButton(
                      icon: Icons.skip_previous,
                      onPressed:
                          _currentPage > 1
                              ? () => _pdfController.previousPage()
                              : null,
                      isEnabled: _currentPage > 1,
                      tooltip: 'الصفحة السابقة',
                    ),

                    // الانتقال لصفحة محددة
                    _buildControlButton(
                      icon: Icons.list,
                      onPressed: () => _showPageSelector(),
                      isEnabled: _totalPages > 0,
                      tooltip: 'الانتقال لصفحة',
                    ),

                    // إعادة تعيين التكبير
                    _buildControlButton(
                      icon: Icons.center_focus_strong,
                      onPressed: _resetZoom,
                      isEnabled: true,
                      tooltip: 'إعادة تعيين التكبير',
                    ),

                    // ملء الشاشة
                    _buildControlButton(
                      icon:
                          _isFullscreen
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                      onPressed: _toggleFullscreen,
                      isEnabled: true,
                      tooltip:
                          _isFullscreen ? 'إنهاء ملء الشاشة' : 'ملء الشاشة',
                    ),

                    // الصفحة التالية
                    _buildControlButton(
                      icon: Icons.skip_next,
                      onPressed:
                          _currentPage < _totalPages
                              ? () => _pdfController.nextPage()
                              : null,
                      isEnabled: _currentPage < _totalPages,
                      tooltip: 'الصفحة التالية',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1F2937).withValues(alpha: 0.95),
            const Color(0xFF111827).withValues(alpha: 0.98),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أنيميشن تحميل عصري
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                return Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF6366F1),
                        const Color(0xFF8B5CF6),
                        const Color(0xFFEC4899),
                      ],
                      stops: [0.0, 0.5, 1.0],
                      transform: GradientRotation(
                        _loadingAnimation.value * 2 * 3.14159,
                      ),
                    ),
                  ),
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFF111827),
                    ),
                    child: const Icon(
                      Icons.picture_as_pdf,
                      color: Colors.white,
                      size: 48,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 32),

            // نص التحميل مع أنيميشن
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: 0.7 + (0.3 * _loadingAnimation.value),
                  child: Text(
                    'جاري تحضير المحتوى...',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // شريط تقدم عصري
            Container(
              width: 200,
              height: 4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Colors.white.withValues(alpha: 0.2),
              ),
              child: AnimatedBuilder(
                animation: _loadingAnimation,
                builder: (context, child) {
                  return FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _loadingAnimation.value,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        gradient: const LinearGradient(
                          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            // نصائح سريعة
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                final tips = [
                  'استخدم إيماءات التكبير والتصغير',
                  'اضغط على الشاشة لإخفاء الأدوات',
                  'استخدم أزرار التنقل السريع',
                ];
                final currentTip =
                    tips[(_loadingAnimation.value * tips.length).floor() %
                        tips.length];

                return Text(
                  '💡 $currentTip',
                  style: GoogleFonts.cairo(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل الملف',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تأكد من اتصالك بالإنترنت وحاول مرة أخرى',
              style: GoogleFonts.cairo(color: Colors.grey, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('العودة', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      ),
    );
  }
}
