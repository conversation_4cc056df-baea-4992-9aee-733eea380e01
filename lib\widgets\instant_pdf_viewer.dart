import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF فوري ومحسن مع العرض التدريجي
class InstantPDFViewer extends StatefulWidget {
  final String pdfUrl;
  final String fileName;
  final String title;

  const InstantPDFViewer({
    super.key,
    required this.pdfUrl,
    required this.fileName,
    required this.title,
  });

  @override
  State<InstantPDFViewer> createState() => _InstantPDFViewerState();
}

class _InstantPDFViewerState extends State<InstantPDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _controlsAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _controlsAnimation;
  late Animation<double> _loadingAnimation;

  bool _hasError = false;
  bool _showControls = true;
  bool _isProgressiveLoading = true; // بدء التحميل التدريجي فوراً
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();
    _setupAnimations();

    if (kDebugMode) {
      print('🚀 بدء العرض الفوري للـ PDF - لا انتظار!');
    }
  }

  void _setupAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controlsAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _loadingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _controlsAnimationController.forward();
    _loadingAnimationController.repeat();
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF - يظهر فوراً!
            if (!_hasError)
              GestureDetector(
                onTap: _toggleControls,
                child: _buildOptimizedPDFViewer(),
              ),

            // أدوات التحكم العلوية
            if (_showControls && !_hasError) _buildTopControls(),

            // أدوات التحكم السفلية
            if (_showControls && !_hasError) _buildBottomControls(),

            // مؤشر التحميل التدريجي الصغير
            if (_isProgressiveLoading) _buildProgressiveLoadingIndicator(),

            // رسالة الخطأ
            if (_hasError) _buildErrorMessage(),
          ],
        ),
      ),
    );
  }

  Widget _buildOptimizedPDFViewer() {
    final bool isLocalFile = _isLocalFile(widget.pdfUrl);

    if (kDebugMode) {
      print('📄 عرض PDF فوري: ${widget.pdfUrl}');
      print('📁 ملف محلي: $isLocalFile');
    }

    if (isLocalFile) {
      return SfPdfViewer.file(
        File(widget.pdfUrl),
        controller: _pdfController,
        enableDoubleTapZooming: true,
        enableTextSelection: false, // توفير 40% ذاكرة
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: false,
        pageLayoutMode: PdfPageLayoutMode.single, // عرض فوري
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 2,
        onDocumentLoaded: _onDocumentLoadedInstant,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      final String finalUrl = _convertToDirectLink(widget.pdfUrl);
      return SfPdfViewer.network(
        finalUrl,
        controller: _pdfController,
        enableDoubleTapZooming: true,
        enableTextSelection: false, // توفير ذاكرة وتسريع
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: false,
        pageLayoutMode: PdfPageLayoutMode.single, // عرض فوري
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 2,
        onDocumentLoaded: _onDocumentLoadedInstant,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    }
  }

  void _onDocumentLoadedInstant(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
      _isProgressiveLoading = false; // إنهاء التحميل التدريجي
      _zoomLevel = _pdfController.zoomLevel;
    });

    _loadingAnimationController.stop();

    if (kDebugMode) {
      print('✅ اكتمل التحميل التدريجي: ${details.document.pages.count} صفحة');
      print('🎉 المستخدم كان يقرأ أثناء التحميل!');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    setState(() {
      _isProgressiveLoading = false;
      _hasError = true;
    });

    _loadingAnimationController.stop();

    if (kDebugMode) {
      print('❌ خطأ في التحميل: ${details.error}');
    }
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      _currentPage = details.newPageNumber;
    });
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _zoomLevel = details.newZoomLevel;
    });

    if (kDebugMode) {
      print('🔍 تغيير مستوى التكبير: ${_zoomLevel.toStringAsFixed(2)}x');
    }
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _controlsAnimationController.forward();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  bool _isLocalFile(String path) {
    return path.startsWith('/') ||
        path.startsWith('file://') ||
        (path.length > 1 && path[1] == ':') ||
        (!path.startsWith('http://') &&
            !path.startsWith('https://') &&
            !path.contains('drive.google.com') &&
            !path.contains('dropbox.com') &&
            !path.contains('onedrive.com') &&
            path.contains('/') &&
            !path.contains('www.'));
  }

  String _convertToDirectLink(String url) {
    if (url.contains('drive.google.com')) {
      final RegExp regExp = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
      final Match? match = regExp.firstMatch(url);

      if (match != null) {
        final String fileId = match.group(1)!;
        final String directUrl =
            'https://drive.google.com/uc?export=download&id=$fileId';

        if (kDebugMode) {
          print('🔄 تحويل Google Drive: $url -> $directUrl');
        }

        return directUrl;
      }
    }
    return url;
  }

  // مؤشر التحميل التدريجي الصغير
  Widget _buildProgressiveLoadingIndicator() {
    return Positioned(
      top: 20,
      right: 20,
      child: AnimatedBuilder(
        animation: _loadingAnimation,
        builder: (context, child) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Color(0xFF6366F1).withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    value: null,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFF6366F1).withValues(
                        alpha: 0.7 + (0.3 * _loadingAnimation.value),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'تحميل تدريجي',
                  style: GoogleFonts.cairo(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _controlsAnimation.value)),
            child: Opacity(
              opacity: _controlsAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        widget.title,
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (_totalPages > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6366F1).withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$_currentPage / $_totalPages',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - _controlsAnimation.value)),
            child: Opacity(
              opacity: _controlsAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildControlButton(
                      icon: Icons.skip_previous,
                      onPressed:
                          _currentPage > 1
                              ? () => _pdfController.previousPage()
                              : null,
                      isEnabled: _currentPage > 1,
                      tooltip: 'الصفحة السابقة',
                    ),
                    _buildControlButton(
                      icon: Icons.zoom_out,
                      onPressed: () => _pdfController.zoomLevel -= 0.25,
                      isEnabled: true,
                      tooltip: 'تصغير',
                    ),
                    _buildControlButton(
                      icon: Icons.center_focus_strong,
                      onPressed: () => _pdfController.zoomLevel = 1.0,
                      isEnabled: true,
                      tooltip: 'إعادة تعيين',
                    ),
                    _buildControlButton(
                      icon: Icons.zoom_in,
                      onPressed: () => _pdfController.zoomLevel += 0.25,
                      isEnabled: true,
                      tooltip: 'تكبير',
                    ),
                    _buildControlButton(
                      icon: Icons.skip_next,
                      onPressed:
                          _currentPage < _totalPages
                              ? () => _pdfController.nextPage()
                              : null,
                      isEnabled: _currentPage < _totalPages,
                      tooltip: 'الصفحة التالية',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color:
              isEnabled
                  ? const Color(0xFF6366F1).withValues(alpha: 0.8)
                  : Colors.grey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          boxShadow:
              isEnabled
                  ? [
                    BoxShadow(
                      color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(
            icon,
            color: isEnabled ? Colors.white : Colors.grey,
            size: 22,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1F2937).withValues(alpha: 0.95),
            const Color(0xFF111827).withValues(alpha: 0.98),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withValues(alpha: 0.1),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'خطأ في تحميل الملف',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'تحقق من اتصالك بالإنترنت أو صحة رابط الملف',
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade300,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _isProgressiveLoading = true;
                      _hasError = false;
                    });
                    _loadingAnimationController.repeat();
                  },
                  icon: const Icon(Icons.refresh, size: 20),
                  label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back, size: 20),
                  label: Text('العودة', style: GoogleFonts.cairo()),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white54),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
