import 'package:hive/hive.dart';

part 'downloaded_pdf.g.dart';

@HiveType(typeId: 4)
class DownloadedPDF extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String originalUrl;

  @HiveField(3)
  String localPath;

  @HiveField(4)
  DateTime downloadDate;

  @HiveField(5)
  int fileSize;

  @HiveField(6)
  String subjectId;

  @HiveField(7)
  String category;

  @HiveField(8)
  bool isAvailable;

  DownloadedPDF({
    required this.id,
    required this.name,
    required this.originalUrl,
    required this.localPath,
    required this.downloadDate,
    required this.fileSize,
    required this.subjectId,
    required this.category,
    this.isAvailable = true,
  });

  factory DownloadedPDF.fromJson(Map<String, dynamic> json) {
    return DownloadedPDF(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      originalUrl: json['originalUrl'] ?? '',
      localPath: json['localPath'] ?? '',
      downloadDate: DateTime.parse(json['downloadDate'] ?? DateTime.now().toIso8601String()),
      fileSize: json['fileSize'] ?? 0,
      subjectId: json['subjectId'] ?? '',
      category: json['category'] ?? '',
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'originalUrl': originalUrl,
      'localPath': localPath,
      'downloadDate': downloadDate.toIso8601String(),
      'fileSize': fileSize,
      'subjectId': subjectId,
      'category': category,
      'isAvailable': isAvailable,
    };
  }

  String get formattedSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(downloadDate);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  @override
  String toString() {
    return 'DownloadedPDF(id: $id, name: $name, localPath: $localPath)';
  }
}
