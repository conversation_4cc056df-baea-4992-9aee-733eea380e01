import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'models/subject.dart';
import 'models/community_post.dart';
import 'models/notification_model.dart';

import 'data/academic_data.dart';

import 'screens/subjects_screen.dart';
import 'screens/chat_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/verification_screen.dart';
import 'screens/privacy_policy_screen.dart';
import 'screens/terms_of_service_screen.dart';
import 'screens/about_app_screen.dart';
// تم إزالة استدعاء AdminDashboardScreen - الصلاحيات مدمجة في الصفحات
import 'providers/theme_provider.dart';

import 'providers/auth_provider.dart' as app_auth;
import 'providers/admin_provider.dart';

import 'providers/connectivity_provider.dart';
import 'config/firebase_config.dart';
import 'services/admin_service.dart';
import 'services/auth_persistence_service.dart';
import 'services/community_service.dart';
import 'services/notification_service.dart';

import 'services/connectivity_service.dart';

import 'services/system_ui_service.dart';

import 'screens/local_downloads_screen.dart';
import 'screens/post_detail_screen.dart';

import 'widgets/offline_indicator.dart';
import 'services/post_cleanup_service.dart';
import 'widgets/skeleton_loading.dart';
import 'widgets/custom_transitions.dart';
import 'utils/responsive_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase وخدمة المصادقة وواجهة النظام - باقي الخدمات ستُهيأ لاحقاً
  try {
    await FirebaseConfig.initialize();
    await AuthPersistenceService.initialize();
    await SystemUIService.initialize();
  } catch (e) {
    debugPrint('خطأ في تهيئة Firebase: $e');
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),

        ChangeNotifierProvider(create: (context) => app_auth.AuthProvider()),
        ChangeNotifierProvider(create: (context) => ConnectivityProvider()),

        ChangeNotifierProvider(create: (context) => AdminProvider()),
      ],
      child: const ShariaLawApp(),
    ),
  );
}

// تهيئة الخدمات في الخلفية بعد فتح التطبيق
Future<void> _initializeServicesInBackground() async {
  try {
    // تهيئة واجهة النظام أولاً
    await SystemUIService.initialize();

    // تم حذف التخزين المحلي مؤقتاً

    // تهيئة خدمة الإشعارات
    await NotificationService.initialize();

    // تهيئة خدمة الاتصال
    await ConnectivityService().initialize();

    // تهيئة الأدمن الرئيسي
    await AdminService.initializeMainAdmin();

    // بدء خدمة تنظيف المنشورات القديمة
    await PostCleanupService.startCleanupService();

    debugPrint('تم تهيئة جميع الخدمات بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة الخدمات: $e');
  }
}

class ShariaLawApp extends StatefulWidget {
  const ShariaLawApp({super.key});

  @override
  State<ShariaLawApp> createState() => _ShariaLawAppState();
}

class _ShariaLawAppState extends State<ShariaLawApp> {
  @override
  void initState() {
    super.initState();
    // تهيئة الخدمات في الخلفية بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServicesInBackground();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        // تحديث شريط الحالة حسب السمة فوراً
        WidgetsBinding.instance.addPostFrameCallback((_) {
          SystemUIService.setStatusBarForTheme(themeProvider.isDarkMode);
        });

        return StatusBarManager(
          statusBarIconBrightness:
              themeProvider.isDarkMode ? Brightness.light : Brightness.dark,
          navigationBarIconBrightness:
              themeProvider.isDarkMode ? Brightness.light : Brightness.dark,
          child: MaterialApp(
            title: 'ساحة الشريعة والقانون',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.currentTheme.copyWith(
              textTheme: themeProvider.currentTheme.textTheme.apply(
                fontFamily: GoogleFonts.cairo().fontFamily,
              ),
            ),
            darkTheme: themeProvider.currentTheme.copyWith(
              textTheme: themeProvider.currentTheme.textTheme.apply(
                fontFamily: GoogleFonts.cairo().fontFamily,
              ),
            ),
            themeMode:
                themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar'), // العربية
            ],
            locale: const Locale('ar'),
            home: Consumer2<app_auth.AuthProvider, ThemeProvider>(
              builder: (context, authProvider, themeProvider, child) {
                // شاشة التحميل أثناء التهيئة
                if (!authProvider.isInitialized) {
                  return StatusBarManager(
                    statusBarIconBrightness:
                        themeProvider.isDarkMode
                            ? Brightness.light
                            : Brightness.dark,
                    child: _buildLoadingScreen(context),
                  );
                }

                // إذا كان المستخدم مسجل الدخول، عرض التطبيق الرئيسي
                if (authProvider.isAuthenticated) {
                  return StatusBarManager(
                    statusBarIconBrightness:
                        themeProvider.isDarkMode
                            ? Brightness.light
                            : Brightness.dark,
                    child: const MainScreen(),
                  );
                }

                // إذا لم يكن مسجل الدخول، عرض شاشة تسجيل الدخول
                return StatusBarManager(
                  statusBarIconBrightness:
                      Brightness.light, // شاشة تسجيل الدخول دائماً مظلمة
                  child: const LoginScreen(),
                );
              },
            ),
            routes: {
              '/home': (context) => const MainScreen(),
              '/login': (context) => const LoginScreen(),
              '/verification':
                  (context) => const VerificationScreen(
                    email: '',
                    password: '',
                    displayName: '',
                  ),
            },
            builder: (context, child) {
              // تحديث شريط الحالة بناءً على الثيم باستخدام الخدمة الجديدة
              WidgetsBinding.instance.addPostFrameCallback((_) {
                SystemUIService.setStatusBarForTheme(themeProvider.isDarkMode);
              });

              return SmartSafeArea(child: child ?? const SizedBox.shrink());
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadingScreen(BuildContext context) {
    return SmartSafeArea(
      child: Scaffold(
        backgroundColor: const Color(0xFF667EEA),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار بسيط
              const Icon(Icons.balance, size: 80, color: Colors.white),
              const SizedBox(height: 24),

              // اسم التطبيق
              const Text(
                'Legal 2025',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),

              // وصف بسيط
              const Text(
                'تطبيق الشريعة والقانون',
                style: TextStyle(fontSize: 16, color: Colors.white70),
              ),
              const SizedBox(height: 32),

              // مؤشر التحميل بسيط
              const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  // تحسين الأداء: إنشاء الصفحات مرة واحدة فقط
  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = const [
      HomeScreen(),
      CommunityScreen(),
      ChatScreen(),
      ProfileScreen(),
    ];

    // تهيئة مزود الاتصال
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ConnectivityProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        // تحديث شريط الحالة حسب الثيم
        WidgetsBinding.instance.addPostFrameCallback((_) {
          SystemUIService.setStatusBarForTheme(themeProvider.isDarkMode);
        });

        return OfflineIndicator(
          child: Scaffold(
            backgroundColor:
                themeProvider.isDarkMode
                    ? const Color(0xFF0F172A)
                    : const Color(0xFFF8FAFC),
            body: _screens[_currentIndex],
            extendBody: true,
            bottomNavigationBar: Container(
              height: 75,
              margin: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      themeProvider.isDarkMode
                          ? [
                            const Color(0xFF1E293B).withValues(alpha: 0.95),
                            const Color(0xFF334155).withValues(alpha: 0.95),
                          ]
                          : [
                            Colors.white.withValues(alpha: 0.95),
                            const Color(0xFFF8FAFC).withValues(alpha: 0.95),
                          ],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color:
                        themeProvider.isDarkMode
                            ? Colors.black.withValues(alpha: 0.4)
                            : Colors.black.withValues(alpha: 0.15),
                    blurRadius: 30,
                    offset: const Offset(0, 10),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 5),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF475569).withValues(alpha: 0.3)
                          : const Color(0xFFE2E8F0).withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(25),
                child: BottomNavigationBar(
                  currentIndex: _currentIndex,
                  onTap: (index) => setState(() => _currentIndex = index),
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  selectedItemColor: const Color(0xFF6366F1),
                  unselectedItemColor:
                      themeProvider.isDarkMode
                          ? const Color(0xFF64748B)
                          : const Color(0xFF6B7280),
                  selectedLabelStyle: GoogleFonts.cairo(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF6366F1),
                  ),
                  unselectedLabelStyle: GoogleFonts.cairo(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF64748B)
                            : const Color(0xFF6B7280),
                  ),
                  type: BottomNavigationBarType.fixed,
                  items: [
                    BottomNavigationBarItem(
                      icon: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient:
                              _currentIndex == 0
                                  ? const LinearGradient(
                                    colors: [
                                      Color(0xFF6366F1),
                                      Color(0xFF8B5CF6),
                                    ],
                                  )
                                  : null,
                          color: _currentIndex == 0 ? null : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow:
                              _currentIndex == 0
                                  ? [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                  : null,
                        ),
                        child: Icon(
                          Icons.home_rounded,
                          size: 24,
                          color:
                              _currentIndex == 0
                                  ? Colors.white
                                  : (themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280)),
                        ),
                      ),
                      label: 'الرئيسية',
                    ),
                    BottomNavigationBarItem(
                      icon: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient:
                              _currentIndex == 1
                                  ? const LinearGradient(
                                    colors: [
                                      Color(0xFF6366F1),
                                      Color(0xFF8B5CF6),
                                    ],
                                  )
                                  : null,
                          color: _currentIndex == 1 ? null : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow:
                              _currentIndex == 1
                                  ? [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                  : null,
                        ),
                        child: Icon(
                          Icons.groups_rounded,
                          size: 24,
                          color:
                              _currentIndex == 1
                                  ? Colors.white
                                  : (themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280)),
                        ),
                      ),
                      label: 'المجتمع',
                    ),
                    BottomNavigationBarItem(
                      icon: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient:
                              _currentIndex == 2
                                  ? const LinearGradient(
                                    colors: [
                                      Color(0xFF6366F1),
                                      Color(0xFF8B5CF6),
                                    ],
                                  )
                                  : null,
                          color: _currentIndex == 2 ? null : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow:
                              _currentIndex == 2
                                  ? [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                  : null,
                        ),
                        child: Stack(
                          children: [
                            Icon(
                              Icons.chat_bubble_rounded,
                              size: 24,
                              color:
                                  _currentIndex == 2
                                      ? Colors.white
                                      : (themeProvider.isDarkMode
                                          ? const Color(0xFF64748B)
                                          : const Color(0xFF6B7280)),
                            ),
                            // TODO: إضافة عداد الرسائل غير المقروءة لاحقاً
                          ],
                        ),
                      ),
                      label: 'الدردشة',
                    ),
                    BottomNavigationBarItem(
                      icon: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient:
                              _currentIndex == 3
                                  ? const LinearGradient(
                                    colors: [
                                      Color(0xFF6366F1),
                                      Color(0xFF8B5CF6),
                                    ],
                                  )
                                  : null,
                          color: _currentIndex == 3 ? null : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow:
                              _currentIndex == 3
                                  ? [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                  : null,
                        ),
                        child: Icon(
                          Icons.person_rounded,
                          size: 24,
                          color:
                              _currentIndex == 3
                                  ? Colors.white
                                  : (themeProvider.isDarkMode
                                      ? const Color(0xFF64748B)
                                      : const Color(0xFF6B7280)),
                        ),
                      ),
                      label: 'الملف الشخصي',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Post Model
class Post {
  final String id;
  final String authorName;
  final String content;
  final DateTime timestamp;
  int likes;
  int comments;
  int shares;
  bool isLiked;
  final String? userRole;
  final bool isPinned;

  Post({
    required this.id,
    required this.authorName,
    required this.content,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.isLiked,
    this.userRole,
    required this.isPinned,
  });
}

// Home Screen
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<AcademicYear> academicYears = AcademicData.getAcademicYears();

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: RefreshIndicator(
              onRefresh: () async {
                // تأثير سحب لطيف للتحديث
                await Future.delayed(const Duration(milliseconds: 800));
                setState(() {});
              },
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFF1D4ED8),
              backgroundColor:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              strokeWidth: 2.5,
              child: CustomScrollView(
                physics: const BouncingScrollPhysics(),
                slivers: [
                  // Modern Hero Header
                  SliverToBoxAdapter(
                    child: Container(
                      height: 160,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFF667EEA),
                            const Color(0xFF764BA2),
                            const Color(0xFF6366F1),
                          ],
                        ),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(32),
                          bottomRight: Radius.circular(32),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF667EEA,
                            ).withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // Header Content
                          Positioned(
                            top: 20,
                            left: 20,
                            right: 20,
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(24),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'أهلاً وسهلاً! 🌟',
                                          style: GoogleFonts.cairo(
                                            fontSize: 24,
                                            fontWeight: FontWeight.w800,
                                            color: Colors.white,
                                            height: 1.2,
                                          ),
                                        ),
                                        const SizedBox(height: 6),
                                        Text(
                                          'مرحباً بك في تطبيق كلية الشريعة والقانون',
                                          style: GoogleFonts.cairo(
                                            fontSize: 14,
                                            color: Colors.white.withValues(
                                              alpha: 0.9,
                                            ),
                                            height: 1.4,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // تم إزالة زر الأدمن - الصلاحيات مدمجة في الصفحات
                                  // Notification Button
                                  Consumer<app_auth.AuthProvider>(
                                    builder: (context, authProvider, child) {
                                      // إظهار الزر دائماً
                                      if (!authProvider.isAuthenticated) {
                                        // زر للمستخدمين غير المسجلين
                                        return Container(
                                          width: 45,
                                          height: 45,
                                          decoration: BoxDecoration(
                                            color: Colors.white.withValues(
                                              alpha: 0.2,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                            border: Border.all(
                                              color: Colors.white.withValues(
                                                alpha: 0.3,
                                              ),
                                              width: 1,
                                            ),
                                          ),
                                          child: IconButton(
                                            onPressed:
                                                () => _showNotifications(),
                                            icon: const Icon(
                                              Icons.notifications_rounded,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                          ),
                                        );
                                      }

                                      return StreamBuilder<int>(
                                        stream:
                                            NotificationService.getUnreadNotificationsCount(),
                                        builder: (context, snapshot) {
                                          final unreadCount =
                                              snapshot.data ?? 0;

                                          return Container(
                                            width: 45,
                                            height: 45,
                                            decoration: BoxDecoration(
                                              color: Colors.white.withValues(
                                                alpha: 0.2,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              border: Border.all(
                                                color: Colors.white.withValues(
                                                  alpha: 0.3,
                                                ),
                                                width: 1,
                                              ),
                                            ),
                                            child: IconButton(
                                              onPressed:
                                                  () => _showNotifications(),
                                              icon: Stack(
                                                children: [
                                                  const Icon(
                                                    Icons.notifications_rounded,
                                                    color: Colors.white,
                                                    size: 24,
                                                  ),
                                                  if (unreadCount > 0)
                                                    Positioned(
                                                      right: 2,
                                                      top: 2,
                                                      child: Container(
                                                        constraints:
                                                            const BoxConstraints(
                                                              minWidth: 16,
                                                              minHeight: 16,
                                                            ),
                                                        padding:
                                                            const EdgeInsets.all(
                                                              2,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          color: const Color(
                                                            0xFFFF4757,
                                                          ),
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                8,
                                                              ),
                                                          border: Border.all(
                                                            color: Colors.white,
                                                            width: 1,
                                                          ),
                                                        ),
                                                        child: Text(
                                                          unreadCount > 99
                                                              ? '99+'
                                                              : unreadCount
                                                                  .toString(),
                                                          style:
                                                              GoogleFonts.cairo(
                                                                color:
                                                                    Colors
                                                                        .white,
                                                                fontSize: 10,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Spacing
                  const SliverToBoxAdapter(child: SizedBox(height: 16)),

                  // Section Title
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الفرق الدراسية',
                            style: GoogleFonts.cairo(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFFF1F5F9)
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            'اختر الفرقة الدراسية للوصول إلى المواد والمحاضرات',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF94A3B8)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                          const SizedBox(height: 12),
                        ],
                      ),
                    ),
                  ),

                  // Academic Years List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate((context, index) {
                        final year = academicYears[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: _buildSimpleYearCard(year, index),
                        );
                      }, childCount: academicYears.length),
                    ),
                  ),

                  // Bottom spacing for floating navigation (مقلل للتناسب)
                  const SliverToBoxAdapter(child: SizedBox(height: 40)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSimpleYearCard(AcademicYear year, int index) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return GestureDetector(
          onTap: () => _navigateToYear(year),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(int.parse(year.color.replaceFirst('#', '0xFF'))),
                  Color(
                    int.parse(year.color.replaceFirst('#', '0xFF')),
                  ).withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Color(
                    int.parse(year.color.replaceFirst('#', '0xFF')),
                  ).withValues(alpha: themeProvider.isDarkMode ? 0.4 : 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        year.arabicName,
                        style: GoogleFonts.cairo(
                          fontSize: 22,
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'كلية الشريعة والقانون',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '${year.semesters.length} فصول دراسية',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Dialog(
                backgroundColor: Colors.transparent,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: MediaQuery.of(context).size.height * 0.7,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors:
                          themeProvider.isDarkMode
                              ? [
                                const Color(0xFF1F2937),
                                const Color(0xFF111827),
                              ]
                              : [
                                const Color(0xFF6366F1),
                                const Color(0xFF4F46E5),
                              ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Header
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF6366F1).withValues(alpha: 0.8),
                              const Color(0xFF4F46E5).withValues(alpha: 0.9),
                            ],
                          ),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.notifications_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'إشعارات الملفات الجديدة',
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Content
                      Expanded(
                        child: _buildPDFNotificationsList(themeProvider),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
    );
  }

  /// بناء قائمة إشعارات PDF
  Widget _buildPDFNotificationsList(ThemeProvider themeProvider) {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );

    if (!authProvider.isAuthenticated) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.login, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'يرجى تسجيل الدخول لعرض الإشعارات',
              style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    final academicYear =
        authProvider.userModel?.academicYear ?? 'الفرقة الأولى';

    return StreamBuilder<List<NotificationModel>>(
      stream: NotificationService.getNotificationsForUser(academicYear),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ في تحميل الإشعارات',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.red[600],
                  ),
                ),
              ],
            ),
          );
        }

        final notifications = snapshot.data ?? [];

        if (notifications.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_off,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد إشعارات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر هنا إشعارات الملفات الجديدة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: notifications.length,
          itemBuilder: (context, index) {
            final notification = notifications[index];
            return _buildNotificationCard(notification, themeProvider);
          },
        );
      },
    );
  }

  /// بناء بطاقة الإشعار
  Widget _buildNotificationCard(
    NotificationModel notification,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode
                ? const Color(0xFF374151).withValues(alpha: 0.8)
                : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              notification.isRead
                  ? Colors.grey.withValues(alpha: 0.3)
                  : const Color(0xFF6366F1).withValues(alpha: 0.5),
          width: notification.isRead ? 1 : 2,
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFF6366F1).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.picture_as_pdf,
            color: Color(0xFF6366F1),
            size: 24,
          ),
        ),
        title: Text(
          notification.title,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
            color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.body,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[300]
                        : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              notification.getRelativeTime(),
              style: GoogleFonts.cairo(fontSize: 11, color: Colors.grey[500]),
            ),
          ],
        ),
        onTap: () {
          // تحديد الإشعار كمقروء
          if (!notification.isRead) {
            NotificationService.markAsRead(notification.id);
          }

          // إغلاق الحوار
          Navigator.of(context).pop();

          // يمكن إضافة منطق التنقل للملف هنا
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم فتح الإشعار: ${notification.title}',
                style: GoogleFonts.cairo(),
              ),
              action: SnackBarAction(
                label: 'اختبار إشعار',
                onPressed: () async {
                  await NotificationService.sendTestNotification();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'تم إرسال إشعار اختبار',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }

  void _navigateToYear(AcademicYear year) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SubjectsScreen(year: year)),
    );
  }

  // تم إزالة دالة التنقل للأدمن - الصلاحيات مدمجة في الصفحات
}

// Community Screen
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  // متغيرات إنشاء المنشور
  // Post creation state
  bool _isCreatingPost = false;
  final TextEditingController _postController = TextEditingController();
  final FocusNode _postFocusNode = FocusNode();
  bool _isAnonymous = false;

  // Poll state
  bool _isPollMode = false;
  final List<TextEditingController> _pollControllers = [
    TextEditingController(),
    TextEditingController(),
  ];

  // Media handling
  PlatformFile? _selectedFile;

  // Media state
  bool _hasFile = false;

  // Comment controllers (kept for compatibility)
  final Map<String, TextEditingController> _commentControllers = {};

  // Real data streams
  Stream<List<CommunityPost>>? _postsStream;
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();
    _initializeStreams();
    _initializeCommunityService();
    _checkNetworkStatus();
  }

  /// فحص حالة الشبكة
  void _checkNetworkStatus() {
    setState(() {
      _isOffline = !ConnectivityService().isConnected;
    });
  }

  /// تحديث المنشورات عند السحب للأسفل
  Future<void> _refreshPosts() async {
    try {
      // إعادة تهيئة stream المنشورات
      setState(() {
        _postsStream = CommunityService.getPostsStream();
      });

      // انتظار قصير للسماح للبيانات بالتحديث
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث المنشورات: $e');
    }
  }

  void _initializeStreams() {
    _postsStream = CommunityService.getPostsStream();
  }

  void _initializeCommunityService() {
    // تهيئة خدمة المجتمع
    WidgetsBinding.instance.addPostFrameCallback((_) {
      CommunityService.initializeStats();
    });
  }

  @override
  void dispose() {
    _postController.dispose();
    _postFocusNode.dispose();
    for (var controller in _pollControllers) {
      controller.dispose();
    }
    for (var controller in _commentControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  // التنقل لصفحة تفاصيل المنشور مع التعليقات المتقدمة
  void _navigateToPostDetail(CommunityPost post) {
    if (kDebugMode) {
      print('🔍 [DEBUG] Navigating to PostDetailScreen for post: ${post.id}');
    }
    Navigator.push(
      context,
      CustomTransitions.slideFromRight(PostDetailScreen(post: post)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: RefreshIndicator(
              onRefresh: _refreshPosts,
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFF1D4ED8),
              backgroundColor:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              child: CustomScrollView(
                physics: const BouncingScrollPhysics(),
                slivers: [
                  // Modern Header
                  SliverToBoxAdapter(
                    child: Container(
                      height: 100,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFF667EEA),
                            const Color(0xFF764BA2),
                            const Color(0xFF6366F1),
                          ],
                        ),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(24),
                          bottomRight: Radius.circular(24),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF667EEA,
                            ).withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'مجتمع الطلاب',
                                        style: GoogleFonts.cairo(
                                          fontSize: 24,
                                          fontWeight: FontWeight.w800,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'شارك وتفاعل مع زملائك',
                                        style: GoogleFonts.cairo(
                                          fontSize: 14,
                                          color: Colors.white.withValues(
                                            alpha: 0.9,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Network Status Indicator
                                if (_isOffline)
                                  Container(
                                    margin: const EdgeInsets.only(left: 8),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.orange.withValues(
                                        alpha: 0.2,
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.orange.withValues(
                                          alpha: 0.5,
                                        ),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.wifi_off,
                                          size: 14,
                                          color: Colors.orange[300],
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'غير متصل',
                                          style: GoogleFonts.cairo(
                                            fontSize: 11,
                                            color: Colors.orange[300],
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Create Post Section
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF1E293B)
                                : Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color:
                                themeProvider.isDarkMode
                                    ? Colors.black.withValues(alpha: 0.3)
                                    : Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Modern Facebook-style Post Creator
                          _buildModernPostCreator(),
                        ],
                      ),
                    ),
                  ),

                  // Posts List
                  SliverToBoxAdapter(
                    child: StreamBuilder<List<CommunityPost>>(
                      stream: _postsStream,
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            itemCount: 3, // عرض 3 skeleton posts
                            itemBuilder:
                                (context, index) => const PostSkeleton(),
                          );
                        }

                        if (snapshot.hasError) {
                          return Consumer<ThemeProvider>(
                            builder: (context, themeProvider, child) {
                              return Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(20),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.error_outline,
                                        size: 48,
                                        color: Colors.red[400],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'خطأ في تحميل المنشورات',
                                        style: GoogleFonts.cairo(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color:
                                              themeProvider.isDarkMode
                                                  ? Colors.red[300]
                                                  : Colors.red[600],
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'اسحب للأسفل للمحاولة مرة أخرى',
                                        style: GoogleFonts.cairo(
                                          fontSize: 14,
                                          color:
                                              themeProvider.isDarkMode
                                                  ? const Color(0xFF94A3B8)
                                                  : const Color(0xFF6B7280),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        }

                        final posts = snapshot.data ?? [];

                        if (posts.isEmpty) {
                          return Center(
                            child: Padding(
                              padding: const EdgeInsets.all(50),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.post_add,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'لا توجد منشورات بعد',
                                    style: GoogleFonts.cairo(
                                      fontSize: 18,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'كن أول من ينشر في المجتمع!',
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      color: Colors.grey[500],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        return RefreshIndicator(
                          onRefresh: () async {
                            setState(() {
                              _postsStream = CommunityService.getPostsStream();
                            });
                          },
                          child: ConstrainedContent(
                            maxWidth:
                                ResponsiveHelper.isTablet(context) ? 600 : null,
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              padding: ResponsiveHelper.getScreenPadding(
                                context,
                              ),
                              itemCount: posts.length,
                              cacheExtent: 800, // تقليل cache لتوفير الذاكرة
                              addAutomaticKeepAlives: false, // تحسين الذاكرة
                              addRepaintBoundaries: false, // تحسين الأداء
                              itemBuilder: (context, index) {
                                final post = posts[index];
                                return StaggeredListAnimation(
                                  index: index,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      bottom:
                                          index < posts.length - 1
                                              ? ResponsiveHelper.getSpacing(
                                                context,
                                                8,
                                              )
                                              : 0,
                                    ),
                                    child: _buildOptimizedPostCard(post),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Bottom spacing
                  const SliverToBoxAdapter(child: SizedBox(height: 100)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOptimizedPostCard(CommunityPost post) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return FadeInAnimation(
          duration: const Duration(milliseconds: 500),
          slideOffset: const Offset(0, 0.1),
          child: Container(
            margin: EdgeInsets.zero,
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              borderRadius: BorderRadius.circular(12), // تقليل الـ radius
              boxShadow: [
                BoxShadow(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.black.withValues(
                            alpha: 0.2,
                          ) // تقليل الشفافية
                          : Colors.black.withValues(alpha: 0.03),
                  blurRadius: 6, // تقليل الـ blur
                  offset: const Offset(0, 1), // تقليل الـ offset
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Profile picture with gradient
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6),
                              const Color(0xFF1D4ED8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(
                                0xFF3B82F6,
                              ).withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            post.authorName.isNotEmpty
                                ? post.authorName[0]
                                : 'م',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Author info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  post.authorName,
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFFF1F5F9)
                                            : const Color(0xFF1F2937),
                                  ),
                                ),
                                if (post.authorId ==
                                    '<EMAIL>') ...[
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF10B981),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      'أدمن',
                                      style: GoogleFonts.cairo(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                                // علامة التثبيت
                                if (post.isPinned) ...[
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF59E0B),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.push_pin,
                                          size: 10,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          'مثبت',
                                          style: GoogleFonts.cairo(
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 14,
                                  color: const Color(0xFF6B7280),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _formatTimestamp(post.timestamp),
                                  style: GoogleFonts.cairo(
                                    fontSize: 13,
                                    color:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFF94A3B8)
                                            : const Color(0xFF6B7280),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Icon(
                                  Icons.public,
                                  size: 14,
                                  color: const Color(0xFF6B7280),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // More options button
                      Container(
                        decoration: BoxDecoration(
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFF334155)
                                  : const Color(0xFFF3F4F6),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          onPressed: () => _showRealPostOptions(post),
                          icon: Icon(
                            Icons.more_horiz,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                          ),
                          iconSize: 20,
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    post.content,
                    style: GoogleFonts.cairo(
                      fontSize: 15,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFFF1F5F9)
                              : const Color(0xFF374151),
                      height: 1.6,
                    ),
                  ),
                ),

                // Poll widget
                if (post.poll != null) ...[
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildModernPollWidget(post),
                  ),
                ],

                const SizedBox(height: 16),

                // Engagement stats
                if (post.likedBy.isNotEmpty) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: const Color(0xFFEF4444),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${post.likedBy.length}',
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: const Color(0xFF6B7280),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Divider(height: 1, color: const Color(0xFFE5E7EB)),
                  ),
                  const SizedBox(height: 12),
                ],

                // Action buttons
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildModernActionButton(
                          icon:
                              post.likedBy.contains(
                                    CommunityService.currentUserId,
                                  )
                                  ? Icons.favorite_rounded
                                  : Icons.favorite_border_rounded,
                          label:
                              post.likedBy.isNotEmpty
                                  ? '${post.likedBy.length}'
                                  : 'إعجاب',
                          color:
                              post.likedBy.contains(
                                    CommunityService.currentUserId,
                                  )
                                  ? const Color(0xFFE11D48) // أحمر أكثر حيوية
                                  : themeProvider.isDarkMode
                                  ? const Color(0xFF94A3B8)
                                  : const Color(0xFF64748B),
                          onTap: () => _toggleRealLike(post),
                        ),
                      ),
                      Expanded(
                        child: FutureBuilder<int>(
                          future: CommunityService.getCommentsCount(post.id),
                          builder: (context, snapshot) {
                            final commentsCount = snapshot.data ?? 0;
                            return _buildModernActionButton(
                              icon: Icons.chat_bubble_outline_rounded,
                              label:
                                  commentsCount > 0
                                      ? '$commentsCount'
                                      : 'تعليق',
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF60A5FA) // أزرق فاتح
                                      : const Color(0xFF3B82F6), // أزرق حيوي
                              onTap: () => _navigateToPostDetail(post),
                            );
                          },
                        ),
                      ),
                      Expanded(
                        child: _buildModernActionButton(
                          icon: Icons.share_rounded,
                          label:
                              'مشاركة ${post.sharedBy.isNotEmpty ? '(${post.sharedBy.length})' : ''}',
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFF34D399) // أخضر فاتح
                                  : const Color(0xFF10B981), // أخضر حيوي
                          onTap: () => _shareRealPost(post),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AnimatedButton(
          onTap: onTap,
          scaleValue: 0.95,
          duration: const Duration(milliseconds: 150),
          borderRadius: BorderRadius.circular(16),
          splashColor: color,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B).withValues(alpha: 0.6)
                      : const Color(0xFFF8FAFC).withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF334155).withValues(alpha: 0.3)
                        : const Color(0xFFE2E8F0).withValues(alpha: 0.5),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.black.withValues(alpha: 0.1)
                          : Colors.black.withValues(alpha: 0.02),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 300),
                  tween: Tween(begin: 0.8, end: 1.0),
                  builder: (context, scale, child) {
                    return Transform.scale(
                      scale: scale,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(icon, color: color, size: 18),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    label,
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: color,
                      letterSpacing: 0.2,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernPollWidget(CommunityPost post) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final poll = post.poll!;
        final options = List<String>.from(poll['options'] ?? []);
        final votes = Map<String, int>.from(poll['votes'] ?? {});
        final totalVotes = votes.values.fold(
          0,
          (total, voteCount) => total + voteCount,
        );

        return Container(
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1F2937).withValues(alpha: 0.8)
                    : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF374151)
                      : const Color(0xFFE5E7EB),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color:
                    themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF8B5CF6,
                            ).withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.poll_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'استطلاع رأي',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                          Text(
                            '$totalVotes ${totalVotes == 1 ? 'صوت' : 'أصوات'}',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.grey[400]
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Poll Options
                ...options.asMap().entries.map((entry) {
                  final index = entry.key;
                  final option = entry.value;
                  final voteCount = votes[index.toString()] ?? 0;
                  final percentage =
                      totalVotes > 0 ? (voteCount / totalVotes * 100) : 0;

                  // ألوان مختلفة لكل خيار
                  final optionColors = [
                    [const Color(0xFF3B82F6), const Color(0xFF1D4ED8)], // أزرق
                    [const Color(0xFF10B981), const Color(0xFF059669)], // أخضر
                    [
                      const Color(0xFFF59E0B),
                      const Color(0xFFD97706),
                    ], // برتقالي
                    [const Color(0xFFEF4444), const Color(0xFFDC2626)], // أحمر
                    [
                      const Color(0xFF8B5CF6),
                      const Color(0xFF7C3AED),
                    ], // بنفسجي
                  ];
                  final colorPair = optionColors[index % optionColors.length];

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: GestureDetector(
                      onTap: () => _voteInFastPoll(post.id, index),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(
                                    0xFF374151,
                                  ).withValues(alpha: 0.5)
                                  : Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF4B5563)
                                    : const Color(0xFFE5E7EB),
                            width: 1,
                          ),
                        ),
                        child: Stack(
                          children: [
                            // شريط التقدم
                            if (totalVotes > 0)
                              Positioned.fill(
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: FractionallySizedBox(
                                    alignment: Alignment.centerLeft,
                                    widthFactor: percentage / 100,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            colorPair[0].withValues(alpha: 0.2),
                                            colorPair[1].withValues(alpha: 0.1),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                            // المحتوى
                            Row(
                              children: [
                                // نقطة ملونة
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(colors: colorPair),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 12),

                                // نص الخيار
                                Expanded(
                                  child: Text(
                                    option,
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color:
                                          themeProvider.isDarkMode
                                              ? Colors.white
                                              : const Color(0xFF1F2937),
                                    ),
                                  ),
                                ),

                                const SizedBox(width: 12),

                                // النتائج
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: colorPair,
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${percentage.toStringAsFixed(0)}%',
                                        style: GoogleFonts.cairo(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      '$voteCount',
                                      style: GoogleFonts.cairo(
                                        fontSize: 10,
                                        color:
                                            themeProvider.isDarkMode
                                                ? Colors.grey[400]
                                                : const Color(0xFF6B7280),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _voteInFastPoll(String postId, int optionIndex) async {
    try {
      // تأثير بصري فوري
      HapticFeedback.lightImpact();

      // إظهار مؤشر تحميل بسيط
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Text('جاري التصويت...', style: GoogleFonts.cairo(fontSize: 14)),
            ],
          ),
          duration: const Duration(seconds: 1),
          backgroundColor: const Color(0xFF8B5CF6),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );

      final success = await CommunityService.voteInPoll(
        postId: postId,
        optionIndex: optionIndex,
      );

      // إخفاء مؤشر التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (success) {
        // تأثير نجاح
        HapticFeedback.mediumImpact();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.check_circle, color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                  Text(
                    'تم التصويت بنجاح!',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      } else {
        // تأثير خطأ
        HapticFeedback.heavyImpact();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'فشل في التصويت',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: const Color(0xFFEF4444),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      HapticFeedback.heavyImpact();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.warning_amber, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Text(
                  'حدث خطأ أثناء التصويت',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: const Color(0xFFF59E0B),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Widget _buildModernPostCreator() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color:
                    themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header Row
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: const Color(0xFF3B82F6),
                    child: Text(
                      'أ',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _isCreatingPost = true;
                        });
                        _postFocusNode.requestFocus();
                      },
                      child: Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF3F4F6),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color:
                                    _isCreatingPost
                                        ? const Color(0xFF3B82F6)
                                        : themeProvider.isDarkMode
                                        ? const Color(0xFF475569)
                                        : const Color(0xFFE5E7EB),
                                width: _isCreatingPost ? 2 : 1,
                              ),
                            ),
                            child: Text(
                              'ما الذي تريد مشاركته؟',
                              style: GoogleFonts.cairo(
                                color:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFF94A3B8)
                                        : const Color(0xFF9CA3AF),
                                fontSize: 16,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),

              // Expanded Content
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child:
                    _isCreatingPost
                        ? _buildExpandedPostContent()
                        : const SizedBox.shrink(),
              ),

              // Action Buttons Row
              if (!_isCreatingPost) ...[
                const SizedBox(height: 12),
                const Divider(height: 1, color: Color(0xFFE5E7EB)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.poll,
                        label: 'استطلاع',
                        color: const Color(0xFF8B5CF6),
                        isActive: _isPollMode,
                        onTap: _togglePollMode,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.attach_file,
                        label: 'ملف',
                        color: const Color(0xFFEF4444),
                        isActive: _hasFile,
                        onTap: _toggleFileMode,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildExpandedPostContent() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          // Text Input Area
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF334155)
                          : const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF475569)
                            : const Color(0xFFE5E7EB),
                  ),
                ),
                child: TextField(
                  controller: _postController,
                  focusNode: _postFocusNode,
                  maxLines: null,
                  minLines: 3,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    height: 1.5,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFFF1F5F9)
                            : const Color(0xFF1F2937),
                  ),
                  decoration: InputDecoration(
                    hintText: 'شاركنا أفكارك وآرائك...',
                    hintStyle: GoogleFonts.cairo(
                      fontSize: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF94A3B8)
                              : const Color(0xFF9CA3AF),
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (value) => setState(() {}),
                ),
              );
            },
          ),

          // عداد الأحرف
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              final currentLength = _postController.text.length;
              final isOverLimit = currentLength > 3000;

              return Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '$currentLength/3000',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            isOverLimit
                                ? Colors.red
                                : (themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF9CA3AF)),
                        fontWeight:
                            isOverLimit ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),

          // Media Attachments
          if (_hasFile) _buildFileAttachment(),
          if (_isPollMode) _buildPollCreation(),

          const SizedBox(height: 16),

          // Bottom Controls
          Row(
            children: [
              // Anonymous Toggle
              _buildAnonymousToggle(),

              const Spacer(),

              // Action Buttons
              TextButton(
                onPressed: _cancelPost,
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(
                    color: const Color(0xFF6B7280),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              ElevatedButton(
                onPressed: _canPublishPost() ? _publishPost : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3B82F6),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'نشر',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border:
                isActive
                    ? Border.all(color: color.withValues(alpha: 0.3))
                    : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isActive ? color : const Color(0xFF6B7280),
                size: 20,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: isActive ? color : const Color(0xFF6B7280),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _togglePollMode() {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = !_isPollMode;
      if (_isPollMode) {
        _hasFile = false;
        _selectedFile = null;
        // التأكد من وجود خيارين على الأقل
        while (_pollControllers.length < 2) {
          _pollControllers.add(TextEditingController());
        }
      }
    });
    _postFocusNode.requestFocus();
  }

  Future<void> _toggleFileMode() async {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = false;
    });

    if (!_hasFile) {
      await _selectFile();
    } else {
      setState(() {
        _hasFile = false;
        _selectedFile = null;
      });
    }
    _postFocusNode.requestFocus();
  }

  Future<void> _selectFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'ppt',
          'pptx',
          'txt',
        ],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFile = result.files.first;
          _hasFile = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في اختيار الملف: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
          ),
        );
      }
    }
  }

  void _cancelPost() {
    setState(() {
      _isCreatingPost = false;
      _isPollMode = false;
      _hasFile = false;
      _isAnonymous = false;
      _selectedFile = null;
      _postController.clear();
      for (var controller in _pollControllers) {
        controller.clear();
      }
    });
  }

  Widget _buildPollCreation() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(top: 12),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors:
                  themeProvider.isDarkMode
                      ? [const Color(0xFF1E293B), const Color(0xFF334155)]
                      : [const Color(0xFFF8FAFF), const Color(0xFFF3F4F6)],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFF8B5CF6).withValues(alpha: 0.2),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.poll_rounded,
                      color: const Color(0xFF8B5CF6),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إنشاء استطلاع تفاعلي',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            color:
                                themeProvider.isDarkMode
                                    ? Colors.white
                                    : const Color(0xFF1F2937),
                          ),
                        ),
                        Text(
                          'اطرح سؤال واحصل على آراء المجتمع',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color:
                                themeProvider.isDarkMode
                                    ? Colors.white70
                                    : const Color(0xFF6B7280),
                          ),
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => setState(() => _isPollMode = false),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.close_rounded,
                        size: 18,
                        color: const Color(0xFFEF4444),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Poll Options
              ...List.generate(_pollControllers.length, (index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF8B5CF6),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF374151)
                                    : Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  _pollControllers[index].text.isNotEmpty
                                      ? const Color(0xFF8B5CF6)
                                      : themeProvider.isDarkMode
                                      ? const Color(0xFF4B5563)
                                      : const Color(0xFFE5E7EB),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.2)
                                        : Colors.black.withValues(alpha: 0.05),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _pollControllers[index],
                            decoration: InputDecoration(
                              hintText: 'اكتب الخيار ${index + 1}...',
                              hintStyle: GoogleFonts.cairo(
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.white54
                                        : const Color(0xFF9CA3AF),
                                fontSize: 14,
                              ),
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                            onChanged: (value) => setState(() {}),
                          ),
                        ),
                      ),
                      if (_pollControllers.length > 2 && index >= 2) ...[
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () => _removePollOption(index),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFFEF4444,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.remove_rounded,
                              color: const Color(0xFFEF4444),
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }),

              // Add Option Button
              if (_pollControllers.length < 5)
                GestureDetector(
                  onTap: _addPollOption,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                        style: BorderStyle.solid,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_rounded,
                          color: const Color(0xFF8B5CF6),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'إضافة خيار جديد',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF8B5CF6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              const SizedBox(height: 12),

              // Poll Info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: const Color(0xFF3B82F6),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'سيتمكن الأعضاء من التصويت مرة واحدة فقط',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: const Color(0xFF3B82F6),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnonymousToggle() {
    return GestureDetector(
      onTap: () => setState(() => _isAnonymous = !_isAnonymous),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              _isAnonymous
                  ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                _isAnonymous
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFFE5E7EB),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _isAnonymous
                  ? Icons.visibility_off
                  : Icons.visibility_off_outlined,
              size: 16,
              color:
                  _isAnonymous
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFF6B7280),
            ),
            const SizedBox(width: 6),
            Text(
              'مجهول',
              style: GoogleFonts.cairo(
                fontSize: 13,
                color:
                    _isAnonymous
                        ? const Color(0xFF3B82F6)
                        : const Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addPollOption() {
    if (_pollControllers.length < 5) {
      setState(() {
        _pollControllers.add(TextEditingController());
      });
    }
  }

  void _removePollOption(int index) {
    if (_pollControllers.length > 2 && index >= 2) {
      setState(() {
        _pollControllers[index].dispose();
        _pollControllers.removeAt(index);
      });
    }
  }

  Widget _buildFileAttachment() {
    final fileColor = _getFileColor(_selectedFile?.extension ?? '');

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            fileColor.withValues(alpha: 0.05),
            fileColor.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: fileColor.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: fileColor.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الملف مع تأثيرات حديثة
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [fileColor, fileColor.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                  color: fileColor.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              _getFileIcon(_selectedFile?.extension ?? ''),
              size: 28,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),

          // معلومات الملف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedFile?.name ?? 'ملف مرفق',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: const Color(0xFF1F2937),
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: fileColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        (_selectedFile?.extension ?? '').toUpperCase(),
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: fileColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatFileSize(_selectedFile?.size ?? 0),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: const Color(0xFF6B7280),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // أزرار التحكم
          Column(
            children: [
              // زر التحميل/الفتح
              GestureDetector(
                onTap: () => _openFile(_selectedFile!),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: fileColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.open_in_new_rounded,
                    size: 20,
                    color: fileColor,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // زر الحذف
              GestureDetector(
                onTap:
                    () => setState(() {
                      _hasFile = false;
                      _selectedFile = null;
                    }),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.close_rounded,
                    size: 20,
                    color: Color(0xFFEF4444),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getFileColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFEF4444);
      case 'doc':
      case 'docx':
        return const Color(0xFF2563EB);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF059669);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFD97706);
      case 'txt':
        return const Color(0xFF6B7280);
      default:
        return const Color(0xFF8B5CF6);
    }
  }

  void _openFile(PlatformFile file) {
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                Text(
                  'خيارات الملف',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 20),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFileOption(
                      icon: Icons.visibility_rounded,
                      label: 'عرض',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم فتح الملف للعرض')),
                        );
                      },
                    ),
                    _buildFileOption(
                      icon: Icons.download_rounded,
                      label: 'تحميل',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم بدء التحميل')),
                        );
                      },
                    ),
                    _buildFileOption(
                      icon: Icons.share_rounded,
                      label: 'مشاركة',
                      color: const Color(0xFF8B5CF6),
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم فتح خيارات المشاركة')),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Widget _buildFileOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF6B7280),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.attach_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Future<void> _publishPost() async {
    await _createRealPost();
  }

  Future<void> _createRealPost() async {
    final content = _postController.text.trim();

    if (content.isEmpty && !_hasFile && !_isPollMode) {
      _showQuickSnackBar('يرجى إضافة محتوى للمنشور', isError: true);
      return;
    }

    // التحقق من حد النص (3000 حرف)
    if (content.length > 3000) {
      _showQuickSnackBar('النص يتجاوز الحد المسموح (3000 حرف)', isError: true);
      return;
    }

    // إظهار مؤشر التحميل
    _showQuickSnackBar('جاري النشر...', duration: 1);

    try {
      String finalContent = content;

      // إضافة محتوى الاستطلاع
      if (_isPollMode) {
        final pollOptions =
            _pollControllers
                .map((c) => c.text.trim())
                .where((text) => text.isNotEmpty)
                .toList();

        if (pollOptions.length >= 2) {
          finalContent +=
              '\n\n📊 استطلاع:\n${pollOptions.map((o) => '• $o').join('\n')}';
        }
      }

      // إضافة الملفات والصور
      if (_selectedFile != null) {
        finalContent += '\n\n📎 ${_selectedFile!.name}';
      }

      // إنشاء المنشور
      await CommunityService.createPost(
        content: finalContent,
        imageUrls: [],
        poll: _isPollMode ? _createPollData() : null,
      );

      // إعادة تعيين النموذج
      _cancelPost();

      // رسالة نجاح
      _showQuickSnackBar('تم النشر بنجاح! 🎉');
    } catch (e) {
      _showQuickSnackBar('خطأ في النشر', isError: true);
    }
  }

  void _showQuickSnackBar(
    String message, {
    bool isError = false,
    int duration = 2,
  }) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: duration),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Map<String, dynamic>? _createPollData() {
    if (!_isPollMode) return null;

    final options =
        _pollControllers
            .map((controller) => controller.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();

    if (options.length < 2) return null;

    return {
      'question': 'استطلاع رأي',
      'options': options,
      'votes': {for (int i = 0; i < options.length; i++) i.toString(): 0},
    };
  }

  bool _canPublishPost() {
    // يمكن النشر إذا كان هناك نص أو ملف أو استطلاع مكتمل
    if (_postController.text.trim().isNotEmpty) return true;
    if (_hasFile) return true;
    if (_isPollMode) {
      // التحقق من وجود خيارين على الأقل مع محتوى
      int validOptions = 0;
      for (var controller in _pollControllers) {
        if (controller.text.trim().isNotEmpty) {
          validOptions++;
        }
      }
      return validOptions >= 2;
    }
    return false;
  }

  // تم حذف دوال الإشعارات لتقليل الحجم

  // Real post interaction methods - محسن للاستجابة الفورية
  Future<void> _toggleRealLike(CommunityPost post) async {
    if (!mounted) return;

    final currentUserId = CommunityService.currentUserId;
    final wasLiked = post.likedBy.contains(currentUserId);

    // تأثير بصري فوري
    if (!wasLiked) {
      // تأثير اهتزاز خفيف للإعجاب
      HapticFeedback.lightImpact();
    }

    // تحديث الواجهة فوراً للاستجابة السريعة
    setState(() {
      if (wasLiked) {
        post.likedBy.remove(currentUserId);
      } else {
        post.likedBy.add(currentUserId);
      }
    });

    // إرسال التحديث للخادم في الخلفية
    try {
      final success = await CommunityService.toggleLike(post.id);

      // إذا فشل، أعد الحالة كما كانت
      if (!success && mounted) {
        setState(() {
          if (wasLiked) {
            post.likedBy.add(currentUserId);
          } else {
            post.likedBy.remove(currentUserId);
          }
        });
      }
    } catch (e) {
      // في حالة الخطأ، أعد الحالة كما كانت
      if (mounted) {
        setState(() {
          if (wasLiked) {
            post.likedBy.add(currentUserId);
          } else {
            post.likedBy.remove(currentUserId);
          }
        });
      }
    }
  }

  void _shareRealPost(CommunityPost post) {
    // تأثير بصري للمشاركة
    HapticFeedback.mediumImpact();

    // تحديث قائمة المشاركة فوراً
    setState(() {
      final currentUserId = CommunityService.currentUserId;
      if (!post.sharedBy.contains(currentUserId)) {
        post.sharedBy.add(currentUserId);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text(
              'تم مشاركة المنشور بنجاح',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showRealPostOptions(CommunityPost post) {
    final isOwner = post.authorId == CommunityService.currentUserId;
    final isAdmin =
        CommunityService.currentUser?.email == '<EMAIL>';

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF475569)
                                : Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Title
                    Text(
                      'خيارات المنشور',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFFF1F5F9)
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Copy text option
                    _buildOptionTile(
                      icon: Icons.copy_outlined,
                      title: 'نسخ النص',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        Clipboard.setData(ClipboardData(text: post.content));
                        _showQuickSnackBar('تم نسخ النص');
                      },
                    ),

                    // Share option
                    _buildOptionTile(
                      icon: Icons.share_outlined,
                      title: 'مشاركة المنشور',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _shareRealPost(post);
                      },
                    ),

                    // Edit option (for owner only)
                    if (isOwner) ...[
                      _buildOptionTile(
                        icon: Icons.edit_outlined,
                        title: 'تعديل المنشور',
                        color: const Color(0xFF8B5CF6),
                        onTap: () {
                          Navigator.pop(context);
                          _showQuickSnackBar('ميزة التعديل قريباً');
                        },
                      ),
                    ],

                    // Pin/Unpin option (for admin only)
                    if (isAdmin) ...[
                      _buildOptionTile(
                        icon:
                            post.isPinned
                                ? Icons.push_pin_outlined
                                : Icons.push_pin,
                        title:
                            post.isPinned ? 'إلغاء التثبيت' : 'تثبيت المنشور',
                        color: const Color(0xFFF59E0B),
                        onTap: () {
                          Navigator.pop(context);
                          _togglePinPost(post);
                        },
                      ),
                    ],

                    // Delete option (for owner and admin)
                    if (isOwner || isAdmin) ...[
                      _buildOptionTile(
                        icon: Icons.delete_outline,
                        title: 'حذف المنشور',
                        color: const Color(0xFFEF4444),
                        onTap: () {
                          Navigator.pop(context);
                          _confirmDeletePost(post);
                        },
                      ),
                    ],

                    // Report option (for others)
                    if (!isOwner) ...[
                      _buildOptionTile(
                        icon: Icons.flag_outlined,
                        title: 'إبلاغ عن المنشور',
                        color: const Color(0xFFEF4444),
                        onTap: () {
                          Navigator.pop(context);
                          _showQuickSnackBar('تم إرسال البلاغ');
                        },
                      ),
                    ],

                    const SizedBox(height: 10),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// تثبيت أو إلغاء تثبيت المنشور
  Future<void> _togglePinPost(CommunityPost post) async {
    try {
      final success = await CommunityService.togglePinPost(post.id);

      if (success) {
        _showQuickSnackBar(
          post.isPinned ? 'تم إلغاء تثبيت المنشور' : 'تم تثبيت المنشور',
          isError: false,
        );

        // تحديث قائمة المنشورات
        setState(() {
          _postsStream = CommunityService.getPostsStream();
        });
      } else {
        _showQuickSnackBar('فشل في تحديث حالة التثبيت', isError: true);
      }
    } catch (e) {
      _showQuickSnackBar('حدث خطأ أثناء تحديث التثبيت', isError: true);
    }
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          title: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFFF1F5F9)
                      : const Color(0xFF374151),
            ),
          ),
          onTap: onTap,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        );
      },
    );
  }

  void _confirmDeletePost(CommunityPost post) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(
              'حذف المنشور',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من حذف هذا المنشور؟ لا يمكن التراجع عن هذا الإجراء.',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deletePost(post);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                  foregroundColor: Colors.white,
                ),
                child: Text('حذف', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Future<void> _deletePost(CommunityPost post) async {
    try {
      _showQuickSnackBar('جاري الحذف...', duration: 1);

      await CommunityService.deletePost(post.id);

      _showQuickSnackBar('تم حذف المنشور بنجاح');
    } catch (e) {
      _showQuickSnackBar('خطأ في حذف المنشور', isError: true);
    }
  }
}

// Profile Screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  String _selectedAcademicYear = 'الفرقة الأولى';

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  // قائمة الفرق الدراسية المتاحة
  final List<String> _availableAcademicYears = [
    'الفرقة الأولى',
    'الفرقة الثانية',
    'الفرقة الثالثة',
    'الفرقة الرابعة',
  ];

  // التحقق من صحة الفرقة الدراسية
  String _validateAcademicYear(String? year) {
    if (year == null || !_availableAcademicYears.contains(year)) {
      return 'الفرقة الأولى'; // القيمة الافتراضية
    }
    return year;
  }

  void _loadUserData() {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );
    if (authProvider.userModel != null) {
      _nameController.text = authProvider.userModel!.displayName;
      _emailController.text = authProvider.userModel!.email;
      _selectedAcademicYear = _validateAcademicYear(
        authProvider.userModel!.academicYear,
      );
    } else if (authProvider.firebaseUser != null) {
      _nameController.text = authProvider.firebaseUser!.displayName ?? 'مستخدم';
      _emailController.text = authProvider.firebaseUser!.email ?? '';
      _selectedAcademicYear = 'الفرقة الأولى';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  // دالة تعديل الملف الشخصي
  Future<void> _showEditProfileDialog() async {
    String tempAcademicYear = _validateAcademicYear(_selectedAcademicYear);

    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF6366F1).withValues(alpha: 0.95),
                      const Color(0xFF8B5CF6).withValues(alpha: 0.95),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.edit_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'تعديل الملف الشخصي',
                                  style: GoogleFonts.cairo(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                Text(
                                  'قم بتحديث بياناتك الشخصية',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(
                              Icons.close_rounded,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Name Field
                            _buildModernTextField(
                              controller: _nameController,
                              label: 'الاسم الكامل',
                              icon: Icons.person_rounded,
                              hint: 'أدخل اسمك الكامل',
                            ),
                            const SizedBox(height: 20),

                            // Academic Year Dropdown
                            _buildModernDropdown(
                              value: tempAcademicYear,
                              label: 'الفرقة الدراسية',
                              icon: Icons.school_rounded,
                              items: _availableAcademicYears,
                              onChanged: (String? newValue) {
                                if (newValue != null) {
                                  setDialogState(() {
                                    tempAcademicYear = newValue;
                                  });
                                }
                              },
                            ),
                            const SizedBox(height: 20),

                            // Email Field (Read-only)
                            _buildModernTextField(
                              controller: _emailController,
                              label: 'البريد الإلكتروني',
                              icon: Icons.email_rounded,
                              hint: 'البريد الإلكتروني',
                              enabled: false,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Actions
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                backgroundColor: Colors.white.withValues(
                                  alpha: 0.2,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                'إلغاء',
                                style: GoogleFonts.cairo(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextButton(
                              onPressed: () async {
                                final navigator = Navigator.of(context);
                                _selectedAcademicYear = tempAcademicYear;
                                await _updateProfile();
                                if (mounted) {
                                  navigator.pop();
                                }
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                backgroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                'حفظ التغييرات',
                                style: GoogleFonts.cairo(
                                  color: const Color(0xFF6366F1),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // دالة تحديث الملف الشخصي
  Future<void> _updateProfile() async {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );

    try {
      final success = await authProvider.updateUserProfile(
        displayName: _nameController.text.trim(),
        academicYear: _selectedAcademicYear,
      );

      if (success && mounted) {
        // تحديث اشتراك إشعارات السنة الدراسية
        final oldYear = authProvider.userModel?.academicYear;
        if (oldYear != _selectedAcademicYear) {
          await NotificationService.updateYearSubscription(
            oldYear,
            _selectedAcademicYear,
          );
        }

        // إعادة تحميل البيانات
        _loadUserData();

        // إشعار بالنجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تم تحديث الملف الشخصي بنجاح',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );

          // تحديث الحالة لإعادة بناء الواجهة
          setState(() {});
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      authProvider.error ?? 'فشل في تحديث الملف الشخصي',
                      style: GoogleFonts.cairo(fontSize: 16),
                    ),
                  ),
                ],
              ),
              backgroundColor: const Color(0xFFEF4444),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }

        // تحديث الحالة لإعادة بناء الواجهة
        setState(() {});
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.warning, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'فشل في تحديث الملف الشخصي',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.warning, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'حدث خطأ أثناء التحديث',
                    style: GoogleFonts.cairo(fontSize: 16),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFF59E0B),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  // دالة عرض صفحة التحميلات المحلية المحسنة
  void _showDownloadsScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LocalDownloadsScreen()),
    );
  }

  // دالة تسجيل الخروج
  Future<void> _handleSignOut() async {
    // عرض dialog للتأكيد
    final bool? shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'تسجيل الخروج',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: GoogleFonts.cairo(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );

    if (shouldSignOut == true && mounted) {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      await authProvider.signOut();

      if (mounted) {
        Navigator.of(
          context,
        ).pushNamedAndRemoveUntil('/login', (route) => false);
      }
    }
  }

  // بناء حقل نص عصري
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    bool enabled = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        enabled: enabled,
        style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 14,
          ),
          hintStyle: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 14,
          ),
          prefixIcon: Icon(
            icon,
            color: Colors.white.withValues(alpha: 0.8),
            size: 20,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  // بناء قائمة منسدلة عصرية
  Widget _buildModernDropdown({
    required String value,
    required String label,
    required IconData icon,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        onChanged: onChanged,
        style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
        dropdownColor: const Color(0xFF1E293B),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 14,
          ),
          prefixIcon: Icon(
            icon,
            color: Colors.white.withValues(alpha: 0.8),
            size: 20,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        items:
            items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(
                  item,
                  style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
                ),
              );
            }).toList(),
      ),
    );
  }

  // الحصول على اسم المستخدم المناسب
  String _getDisplayName() {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );
    if (authProvider.userModel != null) {
      return authProvider.userModel!.displayName;
    } else if (authProvider.firebaseUser != null) {
      return authProvider.firebaseUser!.displayName ?? 'مستخدم';
    }
    return _nameController.text.isNotEmpty
        ? _nameController.text
        : 'مستخدم التطبيق';
  }

  // الحصول على البريد الإلكتروني المناسب
  String _getEmailText() {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );

    // أولاً: تحقق من Firebase User
    if (authProvider.firebaseUser != null &&
        authProvider.firebaseUser!.email != null &&
        authProvider.firebaseUser!.email!.isNotEmpty) {
      return authProvider.firebaseUser!.email!;
    }

    // ثانياً: تحقق من User Model
    if (authProvider.userModel != null &&
        authProvider.userModel!.email.isNotEmpty) {
      return authProvider.userModel!.email;
    }

    // ثالثاً: تحقق من Email Controller
    if (_emailController.text.isNotEmpty) {
      return _emailController.text;
    }

    // افتراضي: إذا لم يوجد بريد إلكتروني
    return '<EMAIL>';
  }

  // الحصول على الفرقة الدراسية المناسبة
  String _getAcademicYear() {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );

    if (authProvider.userModel != null) {
      return _validateAcademicYear(authProvider.userModel!.academicYear);
    }

    return _validateAcademicYear(_selectedAcademicYear);
  }

  // الحصول على لون الفرقة الدراسية
  List<Color> _getAcademicYearColors(String academicYear) {
    switch (academicYear) {
      case 'الفرقة الأولى':
        return [const Color(0xFF6366F1), const Color(0xFF8B5CF6)];
      case 'الفرقة الثانية':
        return [const Color(0xFFEC4899), const Color(0xFFF97316)];
      case 'الفرقة الثالثة':
        return [const Color(0xFF10B981), const Color(0xFF06B6D4)];
      case 'الفرقة الرابعة':
        return [const Color(0xFFF59E0B), const Color(0xFFEF4444)];
      default:
        return [const Color(0xFF6366F1), const Color(0xFF8B5CF6)];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Profile Header
                  Container(
                    height: 280,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(40),
                        bottomRight: Radius.circular(40),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.4),
                          blurRadius: 30,
                          offset: const Offset(0, 15),
                        ),
                        BoxShadow(
                          color: const Color(0xFF6366F1).withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background Pattern
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(32),
                                bottomRight: Radius.circular(32),
                              ),
                            ),
                          ),
                        ),
                        // Profile Content
                        Positioned(
                          top: 40,
                          left: 24,
                          right: 24,
                          child: Column(
                            children: [
                              // Profile Avatar
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(40),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.4),
                                    width: 4,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.2,
                                      ),
                                      blurRadius: 25,
                                      offset: const Offset(0, 10),
                                    ),
                                    BoxShadow(
                                      color: Colors.white.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 15,
                                      offset: const Offset(0, -5),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.person_rounded,
                                  size: 40,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 16),
                              // Name and Academic Year
                              Column(
                                children: [
                                  Text(
                                    _getDisplayName(),
                                    style: GoogleFonts.cairo(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      letterSpacing: 0.3,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  // Academic Year Badge
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: _getAcademicYearColors(
                                          _getAcademicYear(),
                                        ),
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: _getAcademicYearColors(
                                            _getAcademicYear(),
                                          )[0].withValues(alpha: 0.3),
                                          blurRadius: 8,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.school_rounded,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          _getAcademicYear(),
                                          style: GoogleFonts.cairo(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // Email
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.25),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.4),
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.email_rounded,
                                      size: 16,
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        _getEmailText(),
                                        style: GoogleFonts.cairo(
                                          fontSize: 15,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: 0.2,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Role & University
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.white.withValues(alpha: 0.25),
                                          Colors.white.withValues(alpha: 0.15),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(25),
                                      border: Border.all(
                                        color: Colors.white.withValues(
                                          alpha: 0.3,
                                        ),
                                        width: 1.5,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withValues(
                                            alpha: 0.1,
                                          ),
                                          blurRadius: 10,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.school_rounded,
                                          color: Colors.white,
                                          size: 18,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'طالب مسجل',
                                          style: GoogleFonts.cairo(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              // University
                              Text(
                                'كلية الشريعة والقانون',
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white.withValues(alpha: 0.95),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 40),
                  // Settings
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        _buildSettingItem(
                          icon: Icons.edit,
                          title: 'تعديل الملف الشخصي',
                          onTap: _showEditProfileDialog,
                        ),

                        _buildSettingItem(
                          icon: Icons.download,
                          title: 'التحميلات',
                          onTap: _showDownloadsScreen,
                        ),
                        Consumer<ThemeProvider>(
                          builder: (context, themeProvider, child) {
                            return _buildSettingItem(
                              icon:
                                  themeProvider.isDarkMode
                                      ? Icons.dark_mode
                                      : Icons.light_mode,
                              title: 'الوضع المظلم',
                              trailing: Switch(
                                value: themeProvider.isDarkMode,
                                onChanged: (value) {
                                  themeProvider.toggleTheme();
                                },
                                activeColor: const Color(0xFF6366F1),
                              ),
                              onTap: () {
                                themeProvider.toggleTheme();
                              },
                            );
                          },
                        ),

                        _buildSettingItem(
                          icon: Icons.privacy_tip_outlined,
                          title: 'سياسة الخصوصية',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => const PrivacyPolicyScreen(),
                              ),
                            );
                          },
                        ),

                        _buildSettingItem(
                          icon: Icons.description_outlined,
                          title: 'شروط الاستخدام',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => const TermsOfServiceScreen(),
                              ),
                            );
                          },
                        ),

                        _buildSettingItem(
                          icon: Icons.info_outline,
                          title: 'حول التطبيق',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const AboutAppScreen(),
                              ),
                            );
                          },
                        ),

                        _buildSettingItem(
                          icon: Icons.logout,
                          title: 'تسجيل الخروج',
                          onTap: _handleSignOut,
                          isDestructive: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color:
                    (themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.4)
                        : Colors.black.withValues(alpha: 0.12)),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color:
                    (themeProvider.isDarkMode
                        ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                        : const Color(0xFF6366F1).withValues(alpha: 0.05)),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155).withValues(alpha: 0.3)
                      : const Color(0xFFE5E7EB).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(24),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Icon Container
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors:
                              isDestructive
                                  ? [
                                    Colors.red.withValues(alpha: 0.15),
                                    Colors.red.withValues(alpha: 0.1),
                                  ]
                                  : [
                                    const Color(
                                      0xFF6366F1,
                                    ).withValues(alpha: 0.15),
                                    const Color(
                                      0xFF6366F1,
                                    ).withValues(alpha: 0.1),
                                  ],
                        ),
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(
                          color: (isDestructive
                                  ? Colors.red
                                  : const Color(0xFF6366F1))
                              .withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        icon,
                        color:
                            isDestructive
                                ? Colors.red
                                : const Color(0xFF6366F1),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 20),
                    // Title
                    Expanded(
                      child: Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              isDestructive
                                  ? Colors.red
                                  : (themeProvider.isDarkMode
                                      ? const Color(0xFFF1F5F9)
                                      : const Color(0xFF1F2937)),
                          letterSpacing: 0.2,
                        ),
                      ),
                    ),
                    // Trailing
                    if (trailing != null) ...[
                      trailing,
                    ] else ...[
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: (themeProvider.isDarkMode
                                  ? const Color(0xFF334155)
                                  : const Color(0xFFF3F4F6))
                              .withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          size: 14,
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFF94A3B8)
                                  : const Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
