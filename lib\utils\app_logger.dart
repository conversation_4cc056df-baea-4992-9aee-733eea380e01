import 'package:flutter/foundation.dart';

/// مسجل الأحداث للتطبيق
class AppLogger {
  /// تسجيل رسالة معلومات
  static void info(String message, String tag, [dynamic data]) {
    if (kDebugMode) {
      print('ℹ️ [$tag] $message');
      if (data != null) {
        print('   Data: $data');
      }
    }
  }

  /// تسجيل رسالة خطأ
  static void error(String message, String tag, [dynamic error]) {
    if (kDebugMode) {
      print('❌ [$tag] $message');
      if (error != null) {
        print('   Error: $error');
      }
    }
  }

  /// تسجيل رسالة تحذير
  static void warning(String message, String tag, [dynamic data]) {
    if (kDebugMode) {
      print('⚠️ [$tag] $message');
      if (data != null) {
        print('   Data: $data');
      }
    }
  }

  /// تسجيل رسالة نجاح
  static void success(String message, String tag, [dynamic data]) {
    if (kDebugMode) {
      print('✅ [$tag] $message');
      if (data != null) {
        print('   Data: $data');
      }
    }
  }

  /// تسجيل رسالة تصحيح
  static void debug(String message, String tag, [dynamic data]) {
    if (kDebugMode) {
      print('🐛 [$tag] $message');
      if (data != null) {
        print('   Data: $data');
      }
    }
  }

  /// تسجيل أحداث المصادقة
  static void auth(String message, [dynamic data]) {
    info(message, 'AUTH', data);
  }

  /// تسجيل أحداث الشبكة
  static void network(String message, [dynamic data]) {
    info(message, 'NETWORK', data);
  }

  /// تسجيل أحداث قاعدة البيانات
  static void database(String message, [dynamic data]) {
    info(message, 'DATABASE', data);
  }

  /// تسجيل أحداث الإشعارات
  static void notification(String message, [dynamic data]) {
    info(message, 'NOTIFICATION', data);
  }
}
