import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/guest_action_dialog.dart';
import '../screens/login_screen.dart';
import '../screens/register_screen.dart';

/// خدمة حماية الأعمال التي تتطلب تسجيل دخول
class GuestGuardService {
  /// التحقق من تسجيل الدخول وعرض تنبيه إذا لزم الأمر
  static Future<bool> checkAuthAndShowDialog(
    BuildContext context, {
    required String action,
  }) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // إذا كان المستخدم مسجل دخول، السماح بالعمل
    if (authProvider.isAuthenticated) {
      return true;
    }
    
    // إذا كان ضيف، عرض النافذة
    await GuestActionDialog.show(
      context,
      action: action,
      onLoginPressed: () => _navigateToLogin(context),
      onRegisterPressed: () => _navigateToRegister(context),
    );
    
    return false;
  }

  /// التنقل إلى صفحة تسجيل الدخول
  static void _navigateToLogin(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
      ),
    );
  }

  /// التنقل إلى صفحة التسجيل
  static void _navigateToRegister(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RegisterScreen(),
      ),
    );
  }

  /// أعمال محددة مع رسائل مخصصة
  static Future<bool> checkLike(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'الإعجاب بالمنشورات ❤️',
    );
  }

  static Future<bool> checkComment(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'التعليق على المنشورات 💬',
    );
  }

  static Future<bool> checkShare(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'مشاركة المنشورات 📤',
    );
  }

  static Future<bool> checkPost(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'نشر المحتوى 📝',
    );
  }

  static Future<bool> checkChat(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'المشاركة في الدردشة 💭',
    );
  }

  static Future<bool> checkProfile(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'الوصول إلى الملف الشخصي 👤',
    );
  }

  static Future<bool> checkDownload(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'تحميل الملفات 📥',
    );
  }

  static Future<bool> checkBookmark(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'حفظ المنشورات 🔖',
    );
  }

  static Future<bool> checkFollow(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'متابعة المستخدمين 👥',
    );
  }

  static Future<bool> checkNotifications(BuildContext context) {
    return checkAuthAndShowDialog(
      context,
      action: 'استقبال الإشعارات 🔔',
    );
  }

  /// دالة عامة للأعمال المخصصة
  static Future<bool> checkCustomAction(
    BuildContext context,
    String actionName,
  ) {
    return checkAuthAndShowDialog(
      context,
      action: actionName,
    );
  }

  /// التحقق السريع بدون عرض نافذة
  static bool isAuthenticated(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.isAuthenticated;
  }

  /// عرض رسالة بسيطة بدلاً من النافذة
  static void showSimpleMessage(BuildContext context, String action) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('يجب تسجيل الدخول لاستخدام $action'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        action: SnackBarAction(
          label: 'تسجيل الدخول',
          textColor: Colors.white,
          onPressed: () => _navigateToLogin(context),
        ),
      ),
    );
  }
}

/// Extension لتسهيل الاستخدام
extension GuestGuardExtension on BuildContext {
  /// التحقق من الإعجاب
  Future<bool> checkLike() => GuestGuardService.checkLike(this);
  
  /// التحقق من التعليق
  Future<bool> checkComment() => GuestGuardService.checkComment(this);
  
  /// التحقق من المشاركة
  Future<bool> checkShare() => GuestGuardService.checkShare(this);
  
  /// التحقق من النشر
  Future<bool> checkPost() => GuestGuardService.checkPost(this);
  
  /// التحقق من الدردشة
  Future<bool> checkChat() => GuestGuardService.checkChat(this);
  
  /// التحقق من الملف الشخصي
  Future<bool> checkProfile() => GuestGuardService.checkProfile(this);
  
  /// التحقق من التحميل
  Future<bool> checkDownload() => GuestGuardService.checkDownload(this);
  
  /// التحقق من الحفظ
  Future<bool> checkBookmark() => GuestGuardService.checkBookmark(this);
  
  /// التحقق من المتابعة
  Future<bool> checkFollow() => GuestGuardService.checkFollow(this);
  
  /// التحقق من الإشعارات
  Future<bool> checkNotifications() => GuestGuardService.checkNotifications(this);
  
  /// التحقق السريع
  bool get isAuthenticated => GuestGuardService.isAuthenticated(this);
}
