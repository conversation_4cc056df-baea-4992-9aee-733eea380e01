// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'downloaded_pdf.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DownloadedPDFAdapter extends TypeAdapter<DownloadedPDF> {
  @override
  final int typeId = 4;

  @override
  DownloadedPDF read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DownloadedPDF(
      id: fields[0] as String,
      name: fields[1] as String,
      originalUrl: fields[2] as String,
      localPath: fields[3] as String,
      downloadDate: fields[4] as DateTime,
      fileSize: fields[5] as int,
      subjectId: fields[6] as String,
      category: fields[7] as String,
      isAvailable: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, DownloadedPDF obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.originalUrl)
      ..writeByte(3)
      ..write(obj.localPath)
      ..writeByte(4)
      ..write(obj.downloadDate)
      ..writeByte(5)
      ..write(obj.fileSize)
      ..writeByte(6)
      ..write(obj.subjectId)
      ..writeByte(7)
      ..write(obj.category)
      ..writeByte(8)
      ..write(obj.isAvailable);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DownloadedPDFAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
