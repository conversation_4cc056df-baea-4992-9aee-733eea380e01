import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'notification_service.dart';

/// خدمة إدارة ملفات PDF باستخدام Realtime Database
class RealtimePDFService {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static final DatabaseReference _pdfsRef = _database.ref().child('pdfs');

  /// إضافة ملف PDF جديد
  static Future<bool> addPDF({
    required String name,
    required String url,
    required String category,
    required String subjectId,
    required String adminEmail,
  }) async {
    try {
      if (kDebugMode) {
        print('🔥 Realtime DB: بدء إضافة PDF...');
        print('📄 الاسم: $name');
        print('🔗 الرابط: $url');
        print('📂 المادة: $subjectId');
        print('📋 الفئة: $category');
      }

      // إنشاء ID فريد للملف
      final pdfId = _pdfsRef.child(subjectId).child(category).push().key;

      if (pdfId == null) {
        if (kDebugMode) print('❌ فشل في إنشاء ID للملف');
        return false;
      }

      // بيانات الملف - متوافقة مع PDFModel
      final now = DateTime.now();
      final pdfData = {
        'id': pdfId,
        'name': name,
        'url': url,
        'category': category,
        'subjectId': subjectId,
        'subjectName': '', // سيتم ملؤه لاحقاً
        'yearId': '',
        'semesterId': '',
        'createdAt': now.toIso8601String(),
        'updatedAt': now.toIso8601String(),
        'uploadedBy': adminEmail,
        'uploaderName': 'أدمن النظام',
        'downloadCount': 0,
        'fileSize': 0.0,
        'fileName': name,
        'fileExtension': 'pdf',
        'isFromUrl': true,
        'originalUrl': url,
        'isActive': true,
        'metadata': {},
      };

      // إضافة الملف إلى Realtime Database
      final fullPath = 'pdfs/$subjectId/$category/$pdfId';
      if (kDebugMode) {
        print('🔗 المسار الكامل: $fullPath');
        print('📄 البيانات: $pdfData');
      }

      await _pdfsRef.child(subjectId).child(category).child(pdfId).set(pdfData);

      if (kDebugMode) {
        print('✅ تم إضافة PDF بنجاح في Realtime Database: $name');
        print('🔍 يمكنك التحقق في Firebase Console في المسار: $fullPath');
      }

      // إرسال إشعار للمستخدمين في نفس الفرقة الدراسية
      try {
        // الحصول على معلومات المادة لتحديد السنة الدراسية
        final subjectInfo = await _getSubjectInfo(subjectId);
        final academicYear = subjectInfo['academicYear'] ?? 'الفرقة الأولى';
        final subjectName = subjectInfo['name'] ?? 'مادة غير محددة';

        // إرسال إشعار PDF جديد
        await NotificationService.sendNewFileNotification(
          fileName: name,
          subjectName: subjectName,
          academicYear: academicYear,
          category: category,
          fileUrl: url,
        );

        if (kDebugMode) {
          print('✅ تم إرسال إشعار PDF جديد للفرقة: $academicYear');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في إرسال الإشعار: $e');
        }
        // لا نفشل العملية بسبب خطأ في الإشعار
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة PDF إلى Realtime Database: $e');
      }
      return false;
    }
  }

  /// الحصول على معلومات المادة
  static Future<Map<String, dynamic>> _getSubjectInfo(String subjectId) async {
    try {
      // خريطة المواد والسنوات الدراسية
      final subjectsMap = {
        // الفرقة الأولى
        'civil_law_1': {
          'name': 'القانون المدني',
          'academicYear': 'الفرقة الأولى',
        },
        'constitutional_law_1': {
          'name': 'القانون الدستوري',
          'academicYear': 'الفرقة الأولى',
        },
        'criminal_law_1': {
          'name': 'القانون الجنائي',
          'academicYear': 'الفرقة الأولى',
        },
        'administrative_law_1': {
          'name': 'القانون الإداري',
          'academicYear': 'الفرقة الأولى',
        },
        'islamic_law_1': {
          'name': 'الشريعة الإسلامية',
          'academicYear': 'الفرقة الأولى',
        },
        'legal_history_1': {
          'name': 'تاريخ القانون',
          'academicYear': 'الفرقة الأولى',
        },

        // الفرقة الثانية
        'civil_law_2': {
          'name': 'القانون المدني',
          'academicYear': 'الفرقة الثانية',
        },
        'commercial_law_2': {
          'name': 'القانون التجاري',
          'academicYear': 'الفرقة الثانية',
        },
        'criminal_law_2': {
          'name': 'القانون الجنائي',
          'academicYear': 'الفرقة الثانية',
        },
        'administrative_law_2': {
          'name': 'القانون الإداري',
          'academicYear': 'الفرقة الثانية',
        },
        'international_law_2': {
          'name': 'القانون الدولي',
          'academicYear': 'الفرقة الثانية',
        },
        'labor_law_2': {
          'name': 'قانون العمل',
          'academicYear': 'الفرقة الثانية',
        },

        // الفرقة الثالثة
        'civil_procedures_3': {
          'name': 'المرافعات المدنية',
          'academicYear': 'الفرقة الثالثة',
        },
        'criminal_procedures_3': {
          'name': 'المرافعات الجنائية',
          'academicYear': 'الفرقة الثالثة',
        },
        'commercial_law_3': {
          'name': 'القانون التجاري',
          'academicYear': 'الفرقة الثالثة',
        },
        'tax_law_3': {
          'name': 'القانون الضريبي',
          'academicYear': 'الفرقة الثالثة',
        },
        'maritime_law_3': {
          'name': 'القانون البحري',
          'academicYear': 'الفرقة الثالثة',
        },
        'insurance_law_3': {
          'name': 'قانون التأمين',
          'academicYear': 'الفرقة الثالثة',
        },

        // الفرقة الرابعة
        'execution_law_4': {
          'name': 'قانون التنفيذ',
          'academicYear': 'الفرقة الرابعة',
        },
        'bankruptcy_law_4': {
          'name': 'قانون الإفلاس',
          'academicYear': 'الفرقة الرابعة',
        },
        'intellectual_property_4': {
          'name': 'الملكية الفكرية',
          'academicYear': 'الفرقة الرابعة',
        },
        'arbitration_law_4': {
          'name': 'قانون التحكيم',
          'academicYear': 'الفرقة الرابعة',
        },
        'banking_law_4': {
          'name': 'القانون المصرفي',
          'academicYear': 'الفرقة الرابعة',
        },
        'investment_law_4': {
          'name': 'قانون الاستثمار',
          'academicYear': 'الفرقة الرابعة',
        },
      };

      return subjectsMap[subjectId] ??
          {'name': 'مادة غير محددة', 'academicYear': 'الفرقة الأولى'};
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على معلومات المادة: $e');
      }
      return {'name': 'مادة غير محددة', 'academicYear': 'الفرقة الأولى'};
    }
  }

  /// الحصول على ملفات PDF لمادة وفئة معينة
  static Future<List<Map<String, dynamic>>> getPDFs({
    required String subjectId,
    required String category,
  }) async {
    try {
      if (kDebugMode) {
        print('🔥 Realtime DB: جلب PDFs للمادة: $subjectId، الفئة: $category');
      }

      final snapshot =
          await _pdfsRef
              .child(subjectId)
              .child(category)
              .orderByChild('createdAt')
              .get();

      if (!snapshot.exists) {
        if (kDebugMode) print('ℹ️ لا توجد ملفات PDF');
        return [];
      }

      final List<Map<String, dynamic>> pdfs = [];
      final data = snapshot.value as Map<dynamic, dynamic>;

      data.forEach((key, value) {
        if (value is Map && value['isActive'] == true) {
          final pdf = Map<String, dynamic>.from(value);
          pdf['id'] = key; // إضافة المفتاح كـ ID
          pdfs.add(pdf);
        }
      });

      // ترتيب حسب التاريخ (الأحدث أولاً)
      pdfs.sort((a, b) {
        final aTime = a['createdAt'] ?? 0;
        final bTime = b['createdAt'] ?? 0;
        return bTime.compareTo(aTime);
      });

      if (kDebugMode) {
        print('✅ تم جلب ${pdfs.length} ملف PDF');
      }

      return pdfs;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب PDFs: $e');
      }
      return [];
    }
  }

  /// حذف ملف PDF
  static Future<bool> deletePDF({
    required String subjectId,
    required String category,
    required String pdfId,
  }) async {
    try {
      if (kDebugMode) {
        print('🔥 Realtime DB: حذف PDF: $pdfId');
        print('🔗 المسار: pdfs/$subjectId/$category/$pdfId');
      }

      // حذف منطقي (تعيين isActive إلى false)
      await _pdfsRef.child(subjectId).child(category).child(pdfId).update({
        'isActive': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('✅ تم حذف PDF بنجاح من Realtime Database');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف PDF: $e');
        print('❌ نوع الخطأ: ${e.runtimeType}');
      }
      return false;
    }
  }

  /// تحديث ملف PDF
  static Future<bool> updatePDF({
    required String subjectId,
    required String category,
    required String pdfId,
    String? name,
    String? url,
  }) async {
    try {
      if (kDebugMode) {
        print('🔥 Realtime DB: تحديث PDF: $pdfId');
        print('📄 الاسم الجديد: $name');
        print('🔗 الرابط الجديد: $url');
      }

      final updates = <String, dynamic>{};

      if (name != null) {
        updates['name'] = name;
        updates['fileName'] = name; // تحديث fileName أيضاً
      }
      if (url != null) {
        updates['url'] = url;
        updates['originalUrl'] = url; // تحديث originalUrl أيضاً
      }
      updates['updatedAt'] = DateTime.now().toIso8601String();

      if (kDebugMode) {
        print('📊 التحديثات: $updates');
        print('🔗 المسار: pdfs/$subjectId/$category/$pdfId');
      }

      await _pdfsRef
          .child(subjectId)
          .child(category)
          .child(pdfId)
          .update(updates);

      if (kDebugMode) {
        print('✅ تم تحديث PDF بنجاح في Realtime Database');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث PDF: $e');
        print('❌ نوع الخطأ: ${e.runtimeType}');
      }
      return false;
    }
  }

  /// الحصول على جميع ملفات PDF لمادة معينة
  static Future<Map<String, List<Map<String, dynamic>>>> getAllPDFsForSubject({
    required String subjectId,
  }) async {
    try {
      if (kDebugMode) {
        print('🔥 Realtime DB: جلب جميع PDFs للمادة: $subjectId');
      }

      final snapshot = await _pdfsRef.child(subjectId).get();

      if (!snapshot.exists) {
        if (kDebugMode) print('ℹ️ لا توجد ملفات PDF للمادة');
        return {};
      }

      final Map<String, List<Map<String, dynamic>>> result = {};
      final data = snapshot.value as Map<dynamic, dynamic>;

      data.forEach((category, categoryData) {
        if (categoryData is Map) {
          final List<Map<String, dynamic>> pdfs = [];

          categoryData.forEach((pdfId, pdfData) {
            if (pdfData is Map && pdfData['isActive'] == true) {
              final pdf = Map<String, dynamic>.from(pdfData);
              pdf['id'] = pdfId;
              pdfs.add(pdf);
            }
          });

          // ترتيب حسب التاريخ
          pdfs.sort((a, b) {
            final aTime = a['createdAt'] ?? 0;
            final bTime = b['createdAt'] ?? 0;
            return bTime.compareTo(aTime);
          });

          result[category] = pdfs;
        }
      });

      if (kDebugMode) {
        print('✅ تم جلب PDFs لـ ${result.length} فئة');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب جميع PDFs: $e');
      }
      return {};
    }
  }

  /// إحصائيات سريعة
  static Future<Map<String, int>> getStats() async {
    try {
      final snapshot = await _pdfsRef.get();

      if (!snapshot.exists) {
        return {'subjects': 0, 'categories': 0, 'pdfs': 0};
      }

      int subjects = 0;
      int categories = 0;
      int pdfs = 0;

      final data = snapshot.value as Map<dynamic, dynamic>;
      subjects = data.length;

      data.forEach((subjectId, subjectData) {
        if (subjectData is Map) {
          categories += subjectData.length;

          subjectData.forEach((category, categoryData) {
            if (categoryData is Map) {
              categoryData.forEach((pdfId, pdfData) {
                if (pdfData is Map && pdfData['isActive'] == true) {
                  pdfs++;
                }
              });
            }
          });
        }
      });

      return {'subjects': subjects, 'categories': categories, 'pdfs': pdfs};
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الإحصائيات: $e');
      }
      return {'subjects': 0, 'categories': 0, 'pdfs': 0};
    }
  }

  /// مراقبة التغييرات في الوقت الفعلي
  static Stream<List<Map<String, dynamic>>> watchPDFs({
    required String subjectId,
    required String category,
  }) {
    if (kDebugMode) {
      print('🔍 watchPDFs called:');
      print('   📋 Subject: $subjectId');
      print('   📂 Category: $category');
      print('   🔗 Path: pdfs/$subjectId/$category');
    }

    return _pdfsRef
        .child(subjectId)
        .child(category)
        .orderByChild('createdAt')
        .onValue
        .map((event) {
          final List<Map<String, dynamic>> pdfs = [];

          if (kDebugMode) {
            print('🔄 Stream event received:');
            print('   📊 Snapshot exists: ${event.snapshot.exists}');
            print('   📄 Snapshot value: ${event.snapshot.value}');
          }

          if (event.snapshot.exists) {
            final data = event.snapshot.value as Map<dynamic, dynamic>;

            if (kDebugMode) {
              print('   📋 Raw data keys: ${data.keys.toList()}');
            }

            data.forEach((key, value) {
              if (kDebugMode) {
                print('   🔍 Processing key: $key');
                print('   📄 Value type: ${value.runtimeType}');
                print('   📄 Value: $value');
              }

              if (value is Map && value['isActive'] == true) {
                final pdf = Map<String, dynamic>.from(value);
                pdf['id'] = key;
                pdfs.add(pdf);

                if (kDebugMode) {
                  print('   ✅ Added PDF: ${pdf['name']}');
                }
              } else {
                if (kDebugMode) {
                  print(
                    '   ❌ Skipped: isActive=${value is Map ? value['isActive'] : 'not map'}',
                  );
                }
              }
            });

            // ترتيب حسب التاريخ
            pdfs.sort((a, b) {
              final aTime = a['createdAt'] ?? 0;
              final bTime = b['createdAt'] ?? 0;
              return bTime.compareTo(aTime);
            });

            if (kDebugMode) {
              print('   📊 Final PDFs count: ${pdfs.length}');
              for (final pdf in pdfs) {
                print('   📄 PDF: ${pdf['name']} (${pdf['id']})');
              }
            }
          } else {
            if (kDebugMode) {
              print('   ❌ No data found at path: pdfs/$subjectId/$category');
            }
          }

          return pdfs;
        });
  }
}
