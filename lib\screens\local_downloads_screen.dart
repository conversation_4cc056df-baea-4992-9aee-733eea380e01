import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/enhanced_pdf_service.dart';
import '../providers/theme_provider.dart';
import '../widgets/instant_pdf_viewer.dart';

/// شاشة عرض الملفات المحملة محلياً
class LocalDownloadsScreen extends StatefulWidget {
  const LocalDownloadsScreen({super.key});

  @override
  State<LocalDownloadsScreen> createState() => _LocalDownloadsScreenState();
}

class _LocalDownloadsScreenState extends State<LocalDownloadsScreen> {
  List<File> _localFiles = [];
  bool _isLoading = true;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadLocalFiles();
  }

  /// تحميل قائمة الملفات المحلية
  Future<void> _loadLocalFiles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final files = await EnhancedPDFService.getLocalPDFs();
      setState(() {
        _localFiles = files;
        _isLoading = false;
      });

      if (kDebugMode) {
        print('📁 تم تحميل ${files.length} ملف محلي');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الملفات المحلية: $e');
      }
      setState(() {
        _isLoading = false;
      });
      _showMessage('خطأ في تحميل الملفات: $e', isError: true);
    }
  }

  /// تحديث قائمة الملفات
  Future<void> _refreshFiles() async {
    setState(() {
      _isRefreshing = true;
    });

    await _loadLocalFiles();

    setState(() {
      _isRefreshing = false;
    });
  }

  /// حذف ملف محلي
  Future<void> _deleteFile(File file) async {
    final confirmed = await _showDeleteConfirmation(file);
    if (!confirmed) return;

    try {
      final success = await EnhancedPDFService.deleteLocalPDF(file.path);
      if (success) {
        _showMessage('تم حذف الملف بنجاح', isError: false);
        await _loadLocalFiles(); // إعادة تحميل القائمة
      } else {
        _showMessage('فشل في حذف الملف', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في حذف الملف: $e', isError: true);
    }
  }

  /// عرض تأكيد الحذف
  Future<bool> _showDeleteConfirmation(File file) async {
    final fileName = _getFileNameFromPath(file.path);

    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('تأكيد الحذف'),
                content: Text('هل تريد حذف الملف "$fileName"؟'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('إلغاء'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    child: const Text('حذف'),
                  ),
                ],
              ),
        ) ??
        false;
  }

  /// فتح ملف PDF
  void _openPDF(File file) {
    final fileName = _getFileNameFromPath(file.path);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => InstantPDFViewer(
              pdfUrl: file.path, // استخدام المسار المحلي
              fileName: fileName,
              title: fileName,
            ),
      ),
    );
  }

  /// استخراج اسم الملف من المسار
  String _getFileNameFromPath(String path) {
    return path.split('/').last.replaceAll('.pdf', '');
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// تنسيق تاريخ التعديل
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// عرض رسالة للمستخدم
  void _showMessage(String message, {required bool isError}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError
                  ? Icons.error_outline_rounded
                  : Icons.check_circle_outline_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor:
            isError ? const Color(0xFFEF4444) : const Color(0xFF10B981),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: _buildBody(themeProvider),
        );
      },
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      title: Text(
        'التحميلات المحلية',
        style: GoogleFonts.cairo(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6366F1)],
          ),
        ),
      ),
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        // عداد الملفات
        Container(
          margin: const EdgeInsets.only(right: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.folder, size: 16, color: Colors.white),
              const SizedBox(width: 4),
              Text(
                '${_localFiles.length}',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: _isRefreshing ? null : _refreshFiles,
          icon:
              _isRefreshing
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : const Icon(Icons.refresh_rounded),
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          onSelected: (value) async {
            if (value == 'clear_cache') {
              await EnhancedPDFService.clearCache();
              _showMessage('تم تنظيف الكاش بنجاح', isError: false);
            }
          },
          icon: const Icon(Icons.more_vert_rounded, color: Colors.white),
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  value: 'clear_cache',
                  child: Row(
                    children: [
                      Icon(
                        Icons.cleaning_services_rounded,
                        size: 20,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'تنظيف الكاش',
                        style: GoogleFonts.cairo(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody(ThemeProvider themeProvider) {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF667EEA).withValues(alpha: 0.1),
                    const Color(0xFF764BA2).withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
                  strokeWidth: 3,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'جاري تحميل الملفات...',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1E293B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى الانتظار',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    if (_localFiles.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors:
              themeProvider.isDarkMode
                  ? [const Color(0xFF0F172A), const Color(0xFF1E293B)]
                  : [const Color(0xFFF8FAFC), const Color(0xFFF1F5F9)],
        ),
      ),
      child: RefreshIndicator(
        onRefresh: _refreshFiles,
        color: const Color(0xFF667EEA),
        backgroundColor: Colors.white,
        child: ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: _localFiles.length,
          itemBuilder: (context, index) {
            final file = _localFiles[index];
            return _buildFileCard(file, themeProvider);
          },
        ),
      ),
    );
  }

  /// عرض الحالة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة متحركة
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF667EEA).withValues(alpha: 0.1),
                    const Color(0xFF764BA2).withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.cloud_download_rounded,
                size: 60,
                color: Color(0xFF667EEA),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد ملفات محملة',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF1E293B),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'قم بتحميل بعض الملفات من المواد\nلتظهر هنا ويمكنك الوصول إليها بدون إنترنت',
              textAlign: TextAlign.center,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),
            Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: ElevatedButton.icon(
                onPressed: _refreshFiles,
                icon: const Icon(Icons.refresh_rounded, color: Colors.white),
                label: Text(
                  'تحديث القائمة',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الملف
  Widget _buildFileCard(File file, ThemeProvider themeProvider) {
    final fileName = _getFileNameFromPath(file.path);
    final fileStat = file.statSync();
    final fileSize = _formatFileSize(fileStat.size);
    final modifiedDate = _formatDate(fileStat.modified);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color:
                themeProvider.isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color:
              themeProvider.isDarkMode
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openPDF(file),
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // أيقونة PDF محسنة
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFFEF4444),
                        Color(0xFFDC2626),
                        Color(0xFFB91C1C),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFEF4444).withValues(alpha: 0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.picture_as_pdf_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 20),

                // معلومات الملف محسنة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        fileName,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF1E293B),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF667EEA,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.storage_rounded,
                                  size: 12,
                                  color: const Color(0xFF667EEA),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  fileSize,
                                  style: GoogleFonts.cairo(
                                    fontSize: 11,
                                    color: const Color(0xFF667EEA),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.access_time_rounded,
                                  size: 12,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  modifiedDate,
                                  style: GoogleFonts.cairo(
                                    fontSize: 11,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // أزرار الإجراءات
                Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () => _deleteFile(file),
                        icon: Icon(
                          Icons.delete_outline_rounded,
                          color: Colors.red[600],
                          size: 20,
                        ),
                        tooltip: 'حذف الملف',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
