import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import '../models/pdf_model.dart';
import '../services/admin_service.dart';

class AdminProvider with ChangeNotifier {
  bool _isAdmin = false;
  bool _isLoading = false;
  String? _error;
  String? _success;
  AdminUser? _currentAdmin;
  List<PDFModel> _pdfs = [];

  // Getters
  bool get isAdmin => _isAdmin;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get success => _success;
  AdminUser? get currentAdmin => _currentAdmin;
  List<PDFModel> get pdfs => _pdfs;

  /// التحقق من صلاحيات الأدمن
  Future<void> checkAdminStatus(String email) async {
    try {
      _setLoading(true);
      _isAdmin = await AdminService.isAdmin(email);

      if (_isAdmin) {
        await _loadAdminData(email);
        await AdminService.initializeMainAdmin();
        _setSuccess('تم تسجيل الدخول كأدمن');
        if (kDebugMode) print('✅ تم تسجيل الدخول كأدمن: $email');
      } else {
        if (kDebugMode) print('ℹ️ المستخدم ليس أدمن: $email');
      }

      notifyListeners();
    } catch (e) {
      _setError('خطأ في التحقق من صلاحيات الأدمن: $e');
      if (kDebugMode) print('❌ خطأ في التحقق من صلاحيات الأدمن: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل بيانات الأدمن
  Future<void> _loadAdminData(String email) async {
    try {
      final doc =
          await FirebaseFirestore.instance
              .collection(AdminService.adminsCollection)
              .doc(email)
              .get();

      if (doc.exists) {
        _currentAdmin = AdminUser.fromJson(doc.data()!);
      } else if (email == AdminService.mainAdminEmail) {
        _currentAdmin = AdminUser(
          email: email,
          name: 'أمير الشريف',
          permissions: [AdminPermissions.all],
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل بيانات الأدمن: $e');
    }
  }

  /// إضافة PDF جديد
  Future<bool> addPDF({
    required String name,
    required String url,
    required String category,
    required String subjectId,
    required String yearId,
    required String semesterId,
    required double fileSize,
  }) async {
    // تعطيل فحص الصلاحيات مؤقتاً للاختبار
    if (kDebugMode) {
      print('🔧 وضع الاختبار: تجاهل فحص صلاحيات AdminProvider');
      print('   _isAdmin: $_isAdmin');
      print('   _currentAdmin: ${_currentAdmin?.email}');
    }

    // if (!_isAdmin || _currentAdmin == null) {
    //   _setError('ليس لديك صلاحية لإضافة ملفات PDF');
    //   return false;
    // }

    // if (!_currentAdmin!.hasPermission(AdminPermissions.addPDF)) {
    //   _setError('ليس لديك صلاحية لإضافة ملفات PDF');
    //   return false;
    // }

    try {
      _setLoading(true);
      _clearMessages();

      // مؤقتاً نرجع نجاح حتى يتم تطبيق خدمة PDF
      final success = true;

      if (success) {
        _setSuccess('تم إضافة الملف بنجاح وإرسال إشعار للمستخدمين');
        await loadPDFs(); // إعادة تحميل القائمة
        return true;
      } else {
        _setError('فشل في إضافة الملف');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة الملف: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// رفع ملف PDF
  Future<String?> uploadPDF(File file, String fileName) async {
    if (!_isAdmin) {
      _setError('ليس لديك صلاحية لرفع الملفات');
      return null;
    }

    try {
      _setLoading(true);
      _clearMessages();

      final url = await AdminService.uploadPDF(file, fileName);

      if (url != null) {
        _setSuccess('تم رفع الملف بنجاح');
        return url;
      } else {
        _setError('فشل في رفع الملف');
        return null;
      }
    } catch (e) {
      _setError('خطأ في رفع الملف: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث PDF
  Future<bool> updatePDF({
    required String pdfId,
    String? name,
    String? url,
    String? category,
  }) async {
    if (!_isAdmin || _currentAdmin == null) {
      _setError('ليس لديك صلاحية لتعديل ملفات PDF');
      return false;
    }

    if (!_currentAdmin!.hasPermission(AdminPermissions.editPDF)) {
      _setError('ليس لديك صلاحية لتعديل ملفات PDF');
      return false;
    }

    try {
      _setLoading(true);
      _clearMessages();

      final success = await AdminService.updatePDF(
        pdfId: pdfId,
        name: name,
        url: url,
        category: category,
        adminEmail: _currentAdmin!.email,
      );

      if (success) {
        _setSuccess('تم تحديث الملف بنجاح');
        await loadPDFs(); // إعادة تحميل القائمة
        return true;
      } else {
        _setError('فشل في تحديث الملف');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث الملف: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف PDF
  Future<bool> deletePDF(String pdfId) async {
    if (!_isAdmin || _currentAdmin == null) {
      _setError('ليس لديك صلاحية لحذف ملفات PDF');
      return false;
    }

    if (!_currentAdmin!.hasPermission(AdminPermissions.deletePDF)) {
      _setError('ليس لديك صلاحية لحذف ملفات PDF');
      return false;
    }

    try {
      _setLoading(true);
      _clearMessages();

      final success = await AdminService.deletePDF(pdfId, _currentAdmin!.email);

      if (success) {
        _setSuccess('تم حذف الملف بنجاح وإرسال إشعار للمستخدمين');
        await loadPDFs(); // إعادة تحميل القائمة
        return true;
      } else {
        _setError('فشل في حذف الملف');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الملف: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل قائمة PDFs
  Future<void> loadPDFs({
    String? subjectId,
    String? category,
    String? yearId,
  }) async {
    try {
      _setLoading(true);

      // الاستماع للتغييرات في الوقت الفعلي
      AdminService.getPDFsStream(
        subjectId: subjectId,
        category: category,
        yearId: yearId,
      ).listen((snapshot) {
        _pdfs =
            snapshot.docs
                .map(
                  (doc) =>
                      PDFModel.fromJson(doc.data() as Map<String, dynamic>),
                )
                .toList();
        notifyListeners();
      });
    } catch (e) {
      _setError('خطأ في تحميل ملفات PDF: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الخروج من وضع الأدمن
  void signOutAdmin() {
    _isAdmin = false;
    _currentAdmin = null;
    _pdfs.clear();
    _clearMessages();
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _success = null;
    notifyListeners();
  }

  void _setSuccess(String success) {
    _success = success;
    _error = null;
    notifyListeners();
  }

  void _clearMessages() {
    _error = null;
    _success = null;
  }

  void clearMessages() {
    _clearMessages();
    notifyListeners();
  }

  /// تحميل PDFs من Realtime Database لمادة وفئة معينة
  Future<List<Map<String, dynamic>>> loadRealtimePDFs({
    required String subjectId,
    required String category,
  }) async {
    try {
      if (kDebugMode) {
        print('🔥 تحميل PDFs من Realtime Database...');
      }

      // مؤقتاً نرجع قائمة فارغة
      final pdfs = <Map<String, dynamic>>[];

      if (kDebugMode) {
        print('✅ تم تحميل ${pdfs.length} ملف PDF من Realtime Database');
      }

      return pdfs;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل PDFs من Realtime Database: $e');
      }
      return [];
    }
  }

  /// حذف PDF من Realtime Database
  Future<bool> deleteRealtimePDF({
    required String subjectId,
    required String category,
    required String pdfId,
  }) async {
    try {
      _setLoading(true);
      _clearMessages();

      // مؤقتاً نرجع نجاح
      final success = true;

      if (success) {
        _setSuccess('تم حذف الملف بنجاح');
        return true;
      } else {
        _setError('فشل في حذف الملف');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الملف: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
}
