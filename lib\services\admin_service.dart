import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/pdf_model.dart';

class AdminService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // مجموعات Firestore
  static const String pdfsCollection = 'pdfs';
  static const String adminsCollection = 'admins';
  static const String notificationsCollection = 'notifications';
  static const String usersCollection = 'users';

  // الأدمن الرئيسي
  static const String mainAdminEmail = '<EMAIL>';

  /// التحقق من صلاحيات الأدمن
  static Future<bool> isAdmin(String email) async {
    try {
      if (email == mainAdminEmail) return true;

      final doc =
          await _firestore.collection(adminsCollection).doc(email).get();
      if (!doc.exists) return false;

      final admin = AdminUser.fromJson(doc.data()!);
      return admin.isActive;
    } catch (e) {
      if (kDebugMode) print('Error checking admin status: $e');
      return false;
    }
  }

  /// إنشاء الأدمن الرئيسي
  static Future<void> initializeMainAdmin() async {
    try {
      final adminDoc =
          await _firestore
              .collection(adminsCollection)
              .doc(mainAdminEmail)
              .get();

      if (!adminDoc.exists) {
        final mainAdmin = AdminUser(
          email: mainAdminEmail,
          name: 'أمير الشريف',
          permissions: [AdminPermissions.all],
          createdAt: DateTime.now(),
          isActive: true,
        );

        await _firestore
            .collection(adminsCollection)
            .doc(mainAdminEmail)
            .set(mainAdmin.toJson());
        if (kDebugMode) print('✅ تم إنشاء الأدمن الرئيسي: $mainAdminEmail');
      } else {
        if (kDebugMode) print('✅ الأدمن الرئيسي موجود بالفعل: $mainAdminEmail');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إنشاء الأدمن الرئيسي: $e');
      // في حالة عدم وجود صلاحيات، نعتبر الأدمن موجود محلياً
      if (e.toString().contains('permission-denied')) {
        if (kDebugMode) print('🔧 سيتم التحقق من الأدمن محلياً فقط');
      }
    }
  }

  /// رفع ملف PDF مع معلومات شاملة
  static Future<String?> uploadPDF(
    File file,
    String fileName, {
    String? customPath,
    Map<String, String>? metadata,
  }) async {
    try {
      // إنشاء مسار منظم للملف
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = fileName.split('.').last.toLowerCase();
      final cleanFileName = fileName.replaceAll(RegExp(r'[^\w\s-.]'), '');
      final storagePath = customPath ?? 'pdfs/$timestamp-$cleanFileName';

      final ref = _storage.ref().child(storagePath);

      // إضافة metadata للملف
      final settableMetadata = SettableMetadata(
        contentType: 'application/pdf',
        customMetadata: {
          'originalName': fileName,
          'uploadedAt': DateTime.now().toIso8601String(),
          'fileExtension': fileExtension,
          ...?metadata,
        },
      );

      // رفع الملف مع metadata
      final uploadTask = await ref.putFile(file, settableMetadata);
      final downloadUrl = await uploadTask.ref.getDownloadURL();

      if (kDebugMode) {
        print('✅ تم رفع الملف بنجاح:');
        print('   📁 المسار: $storagePath');
        print('   🔗 الرابط: $downloadUrl');
        print('   📊 الحجم: ${file.lengthSync()} bytes');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في رفع الملف: $e');
      return null;
    }
  }

  /// التحقق من صحة ملف PDF
  static bool isValidPDFFile(File file) {
    try {
      // التحقق من امتداد الملف
      final fileName = file.path.split('/').last.toLowerCase();
      if (!fileName.endsWith('.pdf')) {
        return false;
      }

      // التحقق من حجم الملف (أقل من 50 ميجا)
      final fileSizeInMB = file.lengthSync() / (1024 * 1024);
      if (fileSizeInMB > 50) {
        if (kDebugMode) {
          print('❌ حجم الملف كبير جداً: ${fileSizeInMB.toStringAsFixed(1)} MB');
        }
        return false;
      }

      // التحقق من أن الملف ليس فارغ
      if (file.lengthSync() == 0) {
        if (kDebugMode) print('❌ الملف فارغ');
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في التحقق من الملف: $e');
      return false;
    }
  }

  /// حذف ملف من Firebase Storage
  static Future<bool> deleteFileFromStorage(String url) async {
    try {
      final ref = _storage.refFromURL(url);
      await ref.delete();
      if (kDebugMode) print('✅ تم حذف الملف من Storage');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف الملف من Storage: $e');
      return false;
    }
  }

  /// إضافة PDF جديد مع معلومات شاملة
  static Future<bool> addPDF({
    required String name,
    required String url,
    required String category,
    required String subjectId,
    required String subjectName,
    required String yearId,
    required String semesterId,
    required String adminEmail,
    required String adminName,
    required double fileSize,
    required String fileName,
    String fileExtension = 'pdf',
    bool isFromUrl = false,
    String? originalUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // التحقق من صلاحيات الأدمن (مؤقتاً معطل للاختبار)
      if (kDebugMode) {
        print('🔧 وضع الاختبار: تجاهل فحص صلاحيات الأدمن');
        print('📧 البريد المستخدم: $adminEmail');
      }

      // if (adminEmail != mainAdminEmail) {
      //   if (kDebugMode) print('❌ ليس لديك صلاحية إضافة PDF');
      //   return false;
      // }

      final pdfId = _firestore.collection(pdfsCollection).doc().id;

      final pdf = PDFModel(
        id: pdfId,
        name: name,
        url: url,
        category: category,
        subjectId: subjectId,
        subjectName: subjectName,
        yearId: yearId,
        semesterId: semesterId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uploadedBy: adminEmail,
        uploaderName: adminName,
        fileSize: fileSize,
        fileName: fileName,
        fileExtension: fileExtension,
        isFromUrl: isFromUrl,
        originalUrl: originalUrl,
        isActive: true, // تأكد من أن الملف نشط
        metadata: metadata,
      );

      if (kDebugMode) {
        print('📄 إنشاء PDF جديد:');
        print('  - ID: $pdfId');
        print('  - Name: $name');
        print('  - URL: $url');
        print('  - Category: $category');
        print('  - Subject: $subjectId');
        print('  - Active: ${pdf.isActive}');
      }

      try {
        await _firestore
            .collection(pdfsCollection)
            .doc(pdfId)
            .set(pdf.toJson());
        if (kDebugMode) print('✅ تم إضافة PDF في Firebase: $name');
      } catch (e) {
        if (e.toString().contains('permission-denied')) {
          if (kDebugMode) {
            print('⚠️ لا توجد صلاحيات Firebase، سيتم المحاولة مرة أخرى...');
          }
          // إعادة المحاولة مع تأخير قصير
          await Future.delayed(const Duration(seconds: 1));
          try {
            await _firestore
                .collection(pdfsCollection)
                .doc(pdfId)
                .set(pdf.toJson());
            if (kDebugMode) {
              print('✅ تم إضافة PDF في Firebase بعد إعادة المحاولة: $name');
            }
          } catch (e2) {
            if (kDebugMode) {
              print('❌ فشل في إضافة PDF حتى بعد إعادة المحاولة: $e2');
            }
            return false;
          }
        } else {
          rethrow;
        }
      }

      // إرسال إشعار لجميع المستخدمين
      try {
        await _sendPDFNotification(pdf, 'تم إضافة');
      } catch (e) {
        if (kDebugMode) print('⚠️ خطأ في إرسال الإشعار: $e');
      }

      if (kDebugMode) print('✅ تم إضافة PDF جديد: $name');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إضافة PDF: $e');
      return false;
    }
  }

  /// تحديث PDF
  static Future<bool> updatePDF({
    required String pdfId,
    String? name,
    String? url,
    String? category,
    required String adminEmail,
  }) async {
    try {
      final updates = <String, dynamic>{
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (name != null) updates['name'] = name;
      if (url != null) updates['url'] = url;
      if (category != null) updates['category'] = category;

      await _firestore.collection(pdfsCollection).doc(pdfId).update(updates);

      if (kDebugMode) print('✅ تم تحديث PDF: $pdfId');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحديث PDF: $e');
      return false;
    }
  }

  /// حذف PDF
  static Future<bool> deletePDF(String pdfId, String adminEmail) async {
    try {
      // الحصول على بيانات PDF قبل الحذف للإشعار
      final doc = await _firestore.collection(pdfsCollection).doc(pdfId).get();
      if (doc.exists) {
        final pdf = PDFModel.fromJson(doc.data()!);

        // حذف الملف من Storage
        try {
          final ref = _storage.refFromURL(pdf.url);
          await ref.delete();
        } catch (e) {
          if (kDebugMode) print('تحذير: لم يتم حذف الملف من Storage: $e');
        }

        // حذف من Firestore
        await _firestore.collection(pdfsCollection).doc(pdfId).delete();

        // إرسال إشعار
        await _sendPDFNotification(pdf, 'تم حذف');

        if (kDebugMode) print('✅ تم حذف PDF: ${pdf.name}');
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف PDF: $e');
      return false;
    }
  }

  /// الحصول على PDFs حسب المادة والفئة
  static Stream<QuerySnapshot> getPDFsStream({
    String? subjectId,
    String? category,
    String? yearId,
  }) {
    Query query = _firestore.collection(pdfsCollection);

    // تبسيط الاستعلام لتجنب مشكلة الفهرس
    if (subjectId != null && category != null) {
      // استعلام مركب بسيط
      query = query
          .where('subjectId', isEqualTo: subjectId)
          .where('category', isEqualTo: category);
    } else if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    } else if (category != null) {
      query = query.where('category', isEqualTo: category);
    }

    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }

    // ترتيب بسيط بدون فهرس مركب
    try {
      return query.orderBy('createdAt', descending: true).snapshots();
    } catch (e) {
      // في حالة فشل الترتيب، نرجع بدون ترتيب
      return query.snapshots();
    }
  }

  /// إرسال إشعار عن PDF
  static Future<void> _sendPDFNotification(PDFModel pdf, String action) async {
    try {
      // تم إزالة إشعارات PDF لتقليل الحجم
      if (kDebugMode) print('✅ تم إزالة إشعار PDF: ${pdf.name}');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال الإشعار: $e');
    }
  }

  /// زيادة عداد التحميل
  static Future<void> incrementDownloadCount(String pdfId) async {
    try {
      await _firestore.collection(pdfsCollection).doc(pdfId).update({
        'downloadCount': FieldValue.increment(1),
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث عداد التحميل: $e');
    }
  }
}
