import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../utils/app_logger.dart';

/// خدمة مراقبة الاتصال بالإنترنت
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  bool _isConnected = true;
  ConnectivityResult _connectionType = ConnectivityResult.wifi;

  // Stream controllers
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();
  final StreamController<ConnectivityResult> _connectionTypeController =
      StreamController<ConnectivityResult>.broadcast();

  // Getters
  bool get isConnected => _isConnected;
  ConnectivityResult get connectionType => _connectionType;
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;
  Stream<bool> get connectionStream => _connectionStatusController.stream;
  Stream<ConnectivityResult> get connectionTypeStream =>
      _connectionTypeController.stream;

  /// تهيئة خدمة الاتصال
  Future<void> initialize() async {
    try {
      // فحص الحالة الأولية
      await _checkInitialConnection();

      // الاستماع للتغييرات
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          AppLogger.error(
            'خطأ في مراقبة الاتصال',
            'ConnectivityService',
            error,
          );
        },
      );

      AppLogger.success('تم تهيئة خدمة الاتصال بنجاح', 'ConnectivityService');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة الاتصال', 'ConnectivityService', e);
    }
  }

  /// فحص الحالة الأولية للاتصال
  Future<void> _checkInitialConnection() async {
    try {
      final results = await _connectivity.checkConnectivity();
      _onConnectivityChanged(results);
    } catch (e) {
      AppLogger.error('خطأ في فحص الاتصال الأولي', 'ConnectivityService', e);
      _updateConnectionStatus(false, ConnectivityResult.none);
    }
  }

  /// معالج تغيير حالة الاتصال
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    if (results.isEmpty) {
      _updateConnectionStatus(false, ConnectivityResult.none);
      return;
    }

    final result = results.first;
    final isConnected = result != ConnectivityResult.none;

    _updateConnectionStatus(isConnected, result);

    // تسجيل التغيير
    if (isConnected) {
      AppLogger.info(
        'تم الاتصال بالإنترنت: ${_getConnectionTypeName(result)}',
        'ConnectivityService',
      );
    } else {
      AppLogger.warning('انقطع الاتصال بالإنترنت', 'ConnectivityService');
    }
  }

  /// تحديث حالة الاتصال
  void _updateConnectionStatus(
    bool isConnected,
    ConnectivityResult connectionType,
  ) {
    final wasConnected = _isConnected;
    final previousType = _connectionType;

    _isConnected = isConnected;
    _connectionType = connectionType;

    // إرسال التحديثات فقط عند التغيير
    if (wasConnected != isConnected) {
      _connectionStatusController.add(isConnected);
    }

    if (previousType != connectionType) {
      _connectionTypeController.add(connectionType);
    }
  }

  /// الحصول على اسم نوع الاتصال
  String _getConnectionTypeName(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'بيانات الجوال';
      case ConnectivityResult.ethernet:
        return 'إيثرنت';
      case ConnectivityResult.bluetooth:
        return 'بلوتوث';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'أخرى';
      case ConnectivityResult.none:
        return 'لا يوجد اتصال';
    }
  }

  /// فحص الاتصال يدوياً
  Future<bool> checkConnection() async {
    try {
      final results = await _connectivity.checkConnectivity();
      _onConnectivityChanged(results);
      return _isConnected;
    } catch (e) {
      AppLogger.error('خطأ في فحص الاتصال', 'ConnectivityService', e);
      return false;
    }
  }

  /// التحقق من وجود اتصال إنترنت فعلي
  Future<bool> hasInternetConnection() async {
    if (!_isConnected) return false;

    try {
      // محاولة الاتصال بخدمة Google DNS
      final result = await _connectivity.checkConnectivity();
      return result.isNotEmpty && result.first != ConnectivityResult.none;
    } catch (e) {
      AppLogger.error(
        'خطأ في التحقق من الاتصال بالإنترنت',
        'ConnectivityService',
        e,
      );
      return false;
    }
  }

  /// الحصول على معلومات الاتصال
  Map<String, dynamic> getConnectionInfo() {
    return {
      'isConnected': _isConnected,
      'connectionType': _connectionType.toString(),
      'connectionTypeName': _getConnectionTypeName(_connectionType),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// تنظيف الموارد
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
    _connectionTypeController.close();
    AppLogger.info('تم تنظيف خدمة الاتصال', 'ConnectivityService');
  }
}

/// مساعد للتحقق من الاتصال
class ConnectivityHelper {
  static final ConnectivityService _service = ConnectivityService();

  /// التحقق السريع من الاتصال
  static bool get isConnected => _service.isConnected;

  /// نوع الاتصال الحالي
  static ConnectivityResult get connectionType => _service.connectionType;

  /// فحص الاتصال مع رسالة خطأ
  static Future<bool> checkConnectionWithMessage() async {
    final isConnected = await _service.checkConnection();
    if (!isConnected) {
      AppLogger.warning('لا يوجد اتصال بالإنترنت', 'ConnectivityHelper');
    }
    return isConnected;
  }

  /// تنفيذ عملية مع التحقق من الاتصال
  static Future<T?> executeWithConnection<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    if (!isConnected) {
      AppLogger.warning(
        'تم إلغاء العملية بسبب عدم وجود اتصال: ${operationName ?? 'غير محدد'}',
        'ConnectivityHelper',
      );
      return null;
    }

    try {
      return await operation();
    } catch (e) {
      AppLogger.error(
        'خطأ في تنفيذ العملية: ${operationName ?? 'غير محدد'}',
        'ConnectivityHelper',
        e,
      );
      return null;
    }
  }

  /// الانتظار حتى الاتصال
  static Future<void> waitForConnection({
    Duration timeout = const Duration(seconds: 30),
  }) async {
    if (isConnected) return;

    final completer = Completer<void>();
    late StreamSubscription subscription;

    subscription = _service.connectionStatusStream.listen((isConnected) {
      if (isConnected) {
        subscription.cancel();
        completer.complete();
      }
    });

    // إضافة timeout
    Timer(timeout, () {
      if (!completer.isCompleted) {
        subscription.cancel();
        completer.completeError(
          TimeoutException('انتهت مهلة انتظار الاتصال', timeout),
        );
      }
    });

    return completer.future;
  }
}

/// استثناء عدم وجود اتصال
class NoConnectionException implements Exception {
  final String message;
  const NoConnectionException(this.message);

  @override
  String toString() => 'NoConnectionException: $message';
}

/// استثناء انتهاء مهلة الاتصال
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message (${timeout.inSeconds}s)';
}
