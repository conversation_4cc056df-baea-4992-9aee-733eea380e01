import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/app_theme.dart';

/// نافذة تنبيه جميلة للمستخدمين الضيوف
class GuestActionDialog extends StatefulWidget {
  final String action; // نوع العمل المطلوب
  final VoidCallback? onLoginPressed;
  final VoidCallback? onRegisterPressed;

  const GuestActionDialog({
    super.key,
    required this.action,
    this.onLoginPressed,
    this.onRegisterPressed,
  });

  /// عرض النافذة
  static Future<void> show(
    BuildContext context, {
    required String action,
    VoidCallback? onLoginPressed,
    VoidCallback? onRegisterPressed,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder:
          (context) => GuestActionDialog(
            action: action,
            onLoginPressed: onLoginPressed,
            onRegisterPressed: onRegisterPressed,
          ),
    );
  }

  @override
  State<GuestActionDialog> createState() => _GuestActionDialogState();
}

class _GuestActionDialogState extends State<GuestActionDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );

    // بدء الأنيميشن
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Stack(
              children: [
                // الخلفية الرئيسية
                Container(
                  margin: const EdgeInsets.only(top: 40),
                  padding: const EdgeInsets.fromLTRB(24, 60, 24, 24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors:
                          isDark
                              ? [
                                const Color(0xFF1E293B),
                                const Color(0xFF0F172A),
                              ]
                              : [Colors.white, const Color(0xFFF8FAFC)],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 40,
                        offset: const Offset(0, 20),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // العنوان
                      Text(
                        'مرحباً بك! 👋',
                        style: GoogleFonts.cairo(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color:
                              isDark ? Colors.white : const Color(0xFF1E293B),
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 16),

                      // الرسالة
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            height: 1.6,
                            color:
                                isDark
                                    ? const Color(0xFFCBD5E1)
                                    : const Color(0xFF64748B),
                          ),
                          children: [
                            const TextSpan(text: 'لاستخدام ميزة '),
                            TextSpan(
                              text: widget.action,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                            const TextSpan(
                              text: ' يجب عليك تسجيل الدخول أولاً.\n',
                            ),
                            const TextSpan(
                              text: 'أنت حالياً في وضع الضيف 🎭',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // الأزرار
                      Row(
                        children: [
                          // زر تسجيل الدخول
                          Expanded(
                            child: _buildActionButton(
                              text: 'تسجيل الدخول',
                              icon: Icons.login_rounded,
                              isPrimary: true,
                              onPressed: () {
                                Navigator.of(context).pop();
                                widget.onLoginPressed?.call();
                              },
                            ),
                          ),

                          const SizedBox(width: 12),

                          // زر إنشاء حساب
                          Expanded(
                            child: _buildActionButton(
                              text: 'إنشاء حساب',
                              icon: Icons.person_add_rounded,
                              isPrimary: false,
                              onPressed: () {
                                Navigator.of(context).pop();
                                widget.onRegisterPressed?.call();
                              },
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // زر الإغلاق
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          'ربما لاحقاً',
                          style: GoogleFonts.cairo(
                            color:
                                isDark
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF64748B),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // الأيقونة العلوية
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.primaryColor,
                            AppTheme.primaryColor.withValues(alpha: 0.8),
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.primaryColor.withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.person_outline_rounded,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String text,
    required IconData icon,
    required bool isPrimary,
    required VoidCallback onPressed,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 50,
      decoration: BoxDecoration(
        gradient:
            isPrimary
                ? LinearGradient(
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withValues(alpha: 0.8),
                  ],
                )
                : null,
        color:
            isPrimary
                ? null
                : isDark
                ? const Color(0xFF334155)
                : const Color(0xFFF1F5F9),
        borderRadius: BorderRadius.circular(12),
        border:
            isPrimary
                ? null
                : Border.all(
                  color:
                      isDark
                          ? const Color(0xFF475569)
                          : const Color(0xFFE2E8F0),
                ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 20,
                color:
                    isPrimary
                        ? Colors.white
                        : isDark
                        ? const Color(0xFFCBD5E1)
                        : const Color(0xFF475569),
              ),
              const SizedBox(width: 8),
              Text(
                text,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color:
                      isPrimary
                          ? Colors.white
                          : isDark
                          ? const Color(0xFFCBD5E1)
                          : const Color(0xFF475569),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
