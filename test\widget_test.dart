// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:sharia_law_app/main.dart';
import 'package:sharia_law_app/providers/theme_provider.dart';

import 'package:sharia_law_app/providers/auth_provider.dart';
import 'package:sharia_law_app/providers/admin_provider.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => ThemeProvider()),

          ChangeNotifierProvider(create: (context) => AuthProvider()),
          ChangeNotifierProvider(create: (context) => AdminProvider()),
        ],
        child: const ShariaLawApp(),
      ),
    );

    // Verify that the app loads
    await tester.pumpAndSettle();

    // Basic test to ensure app doesn't crash
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
